import requests
import json
import os
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
from tqdm import tqdm
import time
import rsa
from datetime import datetime, timedelta

class ParameterGroup:
    def __init__(self, site_id, site_name, forecast_method, forecast_name):
        self.site_id = site_id
        self.site_name = site_name
        self.forecast_method = forecast_method
        self.forecast_name = forecast_name
    
    def __str__(self):
        return f"Site: {self.site_name} (ID: {self.site_id}), Method: {self.forecast_name} ({self.forecast_method})"

    # # 设置标段id（site_id）
    # site_id = 230265  # CZXZZQ-10果拉山隧道进口左线
    # site_name = "CZXZZQ_10果拉山隧道进口左线"
    # # 设置超报类型
    # forecast_method = 1  # 地震波反射
    # forecast_name = "地震波反射"

    # site_id = 230265  # CZXZZQ-10果拉山隧道进口左线
    # site_name = "CZXZZQ-10果拉山隧道进口左线"
    # forecast_method = 7  # 掌子面素描
    # forecast_name = "掌子面素描"

    # method = 4 #电磁波反射
    # method = 13 #超前水平钻
    # method = 14 #加深炮孔
    # method = 6 #瞬变电磁
# 定义参数组
parameter_groups = [
    # # CZXZZQ_10果拉山隧道进口左线
    # ParameterGroup(230265, "CZXZZQ_10果拉山隧道进口左线", 1, "地震波反射"),
    # ParameterGroup(230265, "CZXZZQ_10果拉山隧道进口左线", 7, "掌子面素描"),
    # ParameterGroup(230265, "CZXZZQ_10果拉山隧道进口左线", 4, "电磁波反射"),
    # ParameterGroup(230265, "CZXZZQ_10果拉山隧道进口左线", 13, "超前水平钻"),
    # ParameterGroup(230265, "CZXZZQ_10果拉山隧道进口左线", 14, "加深炮孔"),
    # ParameterGroup(230265, "CZXZZQ_10果拉山隧道进口左线", 6, "瞬变电磁"),
    # CZXZZQ_10果拉山隧道进口右线
    # ParameterGroup(230267, "CZXZZQ_10果拉山隧道进口右线", 1, "地震波反射"),
    ParameterGroup(230267, "CZXZZQ_10果拉山隧道进口右线", 7, "掌子面素描"),
    # ParameterGroup(230267, "CZXZZQ_10果拉山隧道进口右线", 4, "电磁波反射"),
    # ParameterGroup(230267, "CZXZZQ_10果拉山隧道进口右线", 13, "超前水平钻"),
    # ParameterGroup(230267, "CZXZZQ_10果拉山隧道进口右线", 14, "加深炮孔"),
    # ParameterGroup(230267, "CZXZZQ_10果拉山隧道进口右线", 6, "瞬变电磁"),
    # # CZXZZQ_2色季拉山隧道右线进口TBM
    # ParameterGroup(218239, "CZXZZQ_2色季拉山隧道右线进口TBM", 8, "洞身素描"),
    # ParameterGroup(218239, "CZXZZQ_2色季拉山隧道右线进口TBM", 1, "地震波反射"),
    # ParameterGroup(218239, "CZXZZQ_2色季拉山隧道右线进口TBM", 2, "水平声波剖面"),
    # ParameterGroup(218239, "CZXZZQ_2色季拉山隧道右线进口TBM", 4, "电磁波反射"),
    # ParameterGroup(218239, "CZXZZQ_2色季拉山隧道右线进口TBM", 6, "瞬变电磁"),
    # ParameterGroup(218239, "CZXZZQ_2色季拉山隧道右线进口TBM", 9, "微震监测"),
    # ParameterGroup(218239, "CZXZZQ_2色季拉山隧道右线进口TBM", 0, "其他"),
    # # 215995 CZSCZQ_4对门山隧道进口正洞
    # ParameterGroup(215995, "CZSCZQ_4对门山隧道进口正洞", 1, "地震波反射"),
    # ParameterGroup(215995, "CZSCZQ_4对门山隧道进口正洞", 7, "掌子面素描"),
    # ParameterGroup(215995, "CZSCZQ_4对门山隧道进口正洞", 4, "电磁波反射"),
    # ParameterGroup(215995, "CZSCZQ_4对门山隧道进口正洞", 13, "超前水平钻"),
    # ParameterGroup(215995, "CZSCZQ_4对门山隧道进口正洞", 14, "加深炮孔"),
    # ParameterGroup(215995, "CZSCZQ_4对门山隧道进口正洞", 6, "瞬变电磁"),
]
token = "***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
expiredtime = "2025-06-15 21:39:43"
# 循环处理
for i, group in enumerate(parameter_groups):
    # 如果token不存在，login_message
    # login_message = login("yjjy", "czwlj", "uFtxY2UoudZ57IdfVokl8Q==")

    def encrypt_data(appname, account, password, timestamp):
        # 公钥
        public_key_base64 = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCEJiVVdmEzgRk/Lqg+I8JyavmI6eRZCv9d3rJ/yDGUS0BbxrrUrhdK4t/hLZUjkjtCsX80eLxecn6HkMBHaEM0tQYmZyxpB5NBPHioggQHDrJVskzDdqliHobem+X54INPznlXXwLtRl4vh1/ducI7laD59bVg/I+h/SrepgOvqwIDAQAB"
        
        # 转换为PEM格式并加载公钥
        pem_key = f"-----BEGIN PUBLIC KEY-----\n{public_key_base64}\n-----END PUBLIC KEY-----"
        public_key = rsa.PublicKey.load_pkcs1_openssl_pem(pem_key.encode())
        
        # 创建数据并加密
        data = json.dumps({
            "timestamp": timestamp,
            "appname": appname,
            "password": password,
            "account": account
        })
        
        return rsa.encrypt(data.encode(), public_key).hex()

    def login(appname, account, password):
        timestamp = str(int(time.time() * 1000))
        token = encrypt_data(appname, account, password, timestamp)
        
        response = requests.post(
            'https://sso.r93535.com/api/v1.0/login',
            data={
                'appname': appname,
                'account': account,
                'password': password,
                'timestamp': timestamp,
                'token': token,
                'realUrl': 'https://sso.r93535.com/api/v1.0/login'
            },
            headers = {
                'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'Accept': 'application/json, text/javascript, */*; q=0.01',
                'X-Requested-With': 'XMLHttpRequest',
                'X-Forwarded-For': "*************",  
            }
        )
        
        return response.json()

    def check_and_refresh_token(current_expiredtime):
        """
        检查token是否即将过期或已过期，如果是则重新登录获取新token
        
        参数:
        - current_expiredtime: 当前token的过期时间字符串 (格式: 'YYYY-MM-DD HH:MM:SS')

        
        返回:
        - (token, expiredtime): 返回当前有效的token和过期时间
        """
        # 获取当前时间
        current_time = datetime.now()
        
        # 解析过期时间字符串
        try:
            expired_time = datetime.strptime(current_expiredtime, '%Y-%m-%d %H:%M:%S')
        except ValueError:
            # 如果时间格式解析失败，强制重新登录
            print("时间格式解析失败，重新登录")
            login_message = login("yjjy", "czwlj", "uFtxY2UoudZ57IdfVokl8Q==")
            return login_message['jwt'], login_message['expiredtime']
        
        # 计算过期前5分钟的时间点
        refresh_threshold = expired_time - timedelta(minutes=5)
        
        # 检查是否需要刷新token
        # 条件1: 当前时间在过期前5分钟内
        # 条件2: 当前时间已经超过过期时间
        if current_time >= refresh_threshold:
            print(f"Token即将过期或已过期，当前时间: {current_time.strftime('%Y-%m-%d %H:%M:%S')}, 过期时间: {current_expiredtime}")
            print("正在重新登录...")
            
            # 重新登录
            login_message = login("yjjy", "czwlj", "uFtxY2UoudZ57IdfVokl8Q==")
            
            new_token = login_message['jwt']
            new_expiredtime = login_message['expiredtime']
            
            print(f"登录成功，新的过期时间: {new_expiredtime}")
            return new_token, new_expiredtime
        else:
            # token还有效，返回当前的token
            remaining_time = expired_time - current_time
            print(f"Token仍然有效，剩余时间: {remaining_time}")
            return 0, current_expiredtime


    base_path = r"D:\BaiduSyncdisk\DFN\SynchronizeData"

    # 设置 基础的url
    base_url = "https://apps.r93535.com"

    # 设置 查询超报文件id信息的url
    search_url = f"{base_url}/cqdzyb/site/DZTCinfo"


    # 直接使用对象属性
    site_id = group.site_id
    site_name = group.site_name
    forecast_method = group.forecast_method
    forecast_name = group.forecast_name

    # token = get_token()  # 获取 token 函数
    # token = "***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"

    # 设置 cookies
    cookies = {
        'CRBIMSSOJWT': token
    }
    newtoken, expiredtime = check_and_refresh_token(expiredtime)
    # 如果返回的token是0，说明token还有效，什么也不做，否则更新cookies
    if newtoken != 0:
        cookies = {
            'CRBIMSSOJWT': newtoken
        }
        print(f"新的过期时间: {expiredtime}")

    # 设置请求头，包含必要的信息
    headers = {
        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept': 'application/json, text/javascript, */*; q=0.01',
        'X-Requested-With': 'XMLHttpRequest',
        'Referer': f'{base_url}/cqdzyb/site/siteMin?id={site_id}&type=5',
        'X-Forwarded-For': "*************",  
    }

    # 设置请求的 payload 数据
    payload = {
        'id': str(site_id),
        'method': str(forecast_method),
        # 'kilo': '927136.4',
        'kwflag': '1',  # 控制正序获取或逆序，现在是逆序即最新的先拿
        'page': '1',
        'rows': '5'
    }

    # 发送 POST 请求 先看看总数有多少
    response = requests.post(search_url, headers=headers,
                            cookies=cookies, data=payload)

    # 获取响应内容
    if response.status_code == 200:
        try:
            # 解析 JSON 响应
            total = response.json()['total']

        except json.JSONDecodeError as e:
            print(f"JSON 解析失败无法获取total: {e}")
    else:
        print(f"请求失败，状态码: {response.status_code}")

    payload = {
        'id': str(site_id),
        'method': str(forecast_method),
        # 'kilo': '927136.4',
        'kwflag': '1',  # 控制正序获取或逆序，现在是逆序即最新的先拿
        'page': '1',
        'rows': str(total)
    }

    # 拿到全部
    response = requests.post(search_url, headers=headers,
                            cookies=cookies, data=payload)

    # 获取响应内容
    if response.status_code == 200:
        try:
            # 解析 JSON 响应
            all_rows = response.json()["rows"]

        except json.JSONDecodeError as e:
            print(f"JSON 解析失败无法获取rows: {e}")
    else:
        print(f"请求失败，状态码: {response.status_code}")


    def build_target_urls(file_id, forecast_method):
        """
        构造完整的链接

        Args:
            file_id (int): 文件ID
            forecast_method (int): 预测方法
            base_url (str): 基础URL，默认为 "https://apps.r93535.com"

        Returns:
            dict: 包含两个链接的字典
        """
        if forecast_method == 7:

            # 构造第一个链接 - dzdc/zzmsm
            dzdc_url = f"{base_url}/cqdzyb/dzdc/zzmsm?id={file_id}" #掌子面素描

            # 构造第二个链接 - site/download
            download_url = f"{base_url}/cqdzyb/site/download?id={file_id}&method={forecast_method}"
        if forecast_method == 1:

            # 构造第一个链接 - dzdc/zzmsm
            dzdc_url = f"{base_url}/cqdzyb/wtf/tsp?id={file_id}" #地震波反射

            # 构造第二个链接 - site/download
            download_url = f"{base_url}/cqdzyb/site/download?id={file_id}&method={forecast_method}"
            
        if forecast_method == 4:

            # 构造第一个链接 - dzdc/zzmsm
            dzdc_url = f"{base_url}/cqdzyb/wtf/dcb?id={file_id}" #电磁波反射

            # 构造第二个链接 - site/download
            download_url = f"{base_url}/cqdzyb/site/download?id={file_id}&method={forecast_method}"
            
        if forecast_method == 13:

            # 构造第一个链接 - dzdc/zzmsm
            dzdc_url = f"{base_url}/cqdzyb/ztf/cqspzIndex?id={file_id}" #超前水平钻

            # 构造第二个链接 - site/download
            download_url = f"{base_url}/cqdzyb/site/download?id={file_id}&method={forecast_method}"
            
        if forecast_method == 14:

            # 构造第一个链接 - dzdc/zzmsm
            dzdc_url = f"{base_url}/cqdzyb/ztf/jspk?id={file_id}" #加深炮孔

            # 构造第二个链接 - site/download
            download_url = f"{base_url}/cqdzyb/site/download?id={file_id}&method={forecast_method}"
            
        if forecast_method == 6:

            # 构造第一个链接 - dzdc/zzmsm
            dzdc_url = f"{base_url}/cqdzyb/wtf/sbdc?id={file_id}"#瞬变电磁

            # 构造第二个链接 - site/download
            download_url = f"{base_url}/cqdzyb/site/download?id={file_id}&method={forecast_method}"

        if forecast_method == 8:

            # 构造第一个链接 - dzdc/zzmsm
            dzdc_url = f"{base_url}/cqdzyb/dzdc/dsdzsm?id={file_id}" #洞身素描

            # 构造第二个链接 - site/download
            download_url = f"{base_url}/cqdzyb/site/download?id={file_id}&method={forecast_method}"
        
        if forecast_method == 2:

            # 构造第一个链接 - dzdc/zzmsm
            dzdc_url = f"{base_url}/cqdzyb/wtf/hsp?id={file_id}" #水平声波剖面

            # 构造第二个链接 - site/download
            download_url = f"{base_url}/cqdzyb/site/download?id={file_id}&method={forecast_method}"

        if forecast_method == 9:

            # 构造第一个链接 - dzdc/zzmsm
            dzdc_url = f"{base_url}/cqdzyb/wtf/wz?id={file_id}" #微震监测

            # 构造第二个链接 - site/download
            download_url = f"{base_url}/cqdzyb/site/download?id={file_id}&method={forecast_method}"

        if forecast_method == 0:
            # 没有第一个链接 - dzdc/zzmsm
            dzdc_url = None #其他

            # 构造第二个链接 - site/download
            download_url = f"{base_url}/cqdzyb/site/download?id={file_id}&method={forecast_method}"

        return {
            "dzdc_url": dzdc_url,
            "download_url": download_url
        }


    def batch_build_urls(all_rows):
        url_results = []

        for index, row in enumerate(all_rows):
            if not isinstance(row, dict):
                print(f"警告: 第{index}行不是字典类型: {row}")
                continue

            # 检查必需的字段
            file_id = row.get('ID')
            dk = row.get('DKILO')
            m_date = row.get('MONITORDATE')

            # 记录缺失的字段
            missing_fields = []
            if file_id is None:
                missing_fields.append('ID')
            if dk is None:
                missing_fields.append('DKILO')
            if m_date is None:
                missing_fields.append('MONITORDATE')

            if missing_fields:
                print(f"警告: 第{index}行缺少字段 {missing_fields}: {row}")

            # 只有file_id存在才构造URL
            if file_id is not None:
                try:
                    urls = build_target_urls(file_id, forecast_method)
                    url_results.append({
                        'file_id': file_id,
                        'dks': dk,
                        'monitor_date': m_date,
                        'dzdc_url': urls['dzdc_url'],
                        'download_url': urls['download_url']
                    })
                except Exception as e:
                    print(f"警告: 构造file_id {file_id} 的URL时出错: {e}")

        return url_results


    def filter_existing_data(target_urls_info, base_path, site_name, forecast_name):
        """
        检查本地数据并过滤已存在的目标数据项
        
        Args:
            target_urls_info: 包含目标URL信息的字典列表
            base_path: 基础路径
            site_name: 站点名称
            forecast_name: 预报名称
            
        Returns:
            tuple: (过滤后的数据列表, 统计信息字符串)
        """
        
        # 构建完整的目录路径
        json_dir_path = os.path.join(base_path, site_name, forecast_name, "json")
        
        # 检查目录是否存在
        if not os.path.exists(json_dir_path) or not os.path.isdir(json_dir_path):
            return target_urls_info, "无本地数据，将下载所有目标数据"
        
        # 获取目录中所有的JSON文件名（不包含扩展名）
        try:
            json_files = [f for f in os.listdir(json_dir_path) if f.endswith('.json')]
            existing_dks_set = set()
            
            for json_file in json_files:
                # 提取文件名中的dks值（去掉.json扩展名）
                dks_str = json_file.replace('.json', '')
                try:
                    # 使用float而不是int来保持小数点
                    dks_value = float(dks_str)
                    existing_dks_set.add(dks_value)
                except ValueError:
                    # 如果文件名不是有效数字，跳过该文件
                    continue
                    
        except OSError as e:
            # 如果读取目录时出现错误，返回原始数据
            return target_urls_info, f"读取本地目录时出现错误: {str(e)}，将下载所有目标数据"
        
        # 统计原始数据量
        original_count = len(target_urls_info)
        
        # 过滤掉本地已存在的数据项
        filtered_data = []
        removed_count = 0
        
        for item in target_urls_info:
            dks_value = item.get('dks')
            if dks_value is not None and dks_value in existing_dks_set:
                # 该dks对应的JSON文件已存在，跳过此项
                removed_count += 1
            else:
                # 该dks对应的JSON文件不存在，保留此项
                filtered_data.append(item)
        
        # 计算需要下载的数量
        download_count = len(filtered_data)
        
        # 生成统计信息
        if removed_count == 0:
            stats_message = f"总共{original_count}条数据，本地无重复数据，将下载全部{download_count}条"
        elif download_count == 0:
            stats_message = f"总共{original_count}条数据，全部已存在于本地，无需下载"
        else:
            stats_message = f"总共{original_count}条数据，剔除{removed_count}条本地已有数据，即将下载{download_count}条"
        
        return filtered_data, stats_message

    target_urls_info = batch_build_urls(all_rows)
    # target_urls_info = target_urls_info[0:6]
    # 过滤掉本地已存在的数据项
    target_urls_info_not_existing, stats_message = filter_existing_data(
        target_urls_info, base_path, site_name, forecast_name)


    def create_directories(base_path, site_name, forecast_name):
        """创建所需的目录结构"""
        web_path = os.path.join(base_path, site_name, forecast_name, "web")
        raw_path = os.path.join(base_path, site_name, forecast_name, "raw")
        json_path = os.path.join(base_path, site_name, forecast_name, "json")
        attach_path = os.path.join(base_path, site_name, forecast_name, "attach")

        os.makedirs(web_path, exist_ok=True)
        os.makedirs(raw_path, exist_ok=True)
        os.makedirs(json_path, exist_ok=True)
        os.makedirs(attach_path, exist_ok=True)

        return web_path, raw_path, json_path, attach_path


    def create_session(cookies, headers=None):
        """创建请求会话"""
        if headers is None:
            headers = {
                'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'Accept': 'application/json, text/javascript, */*; q=0.01',
                'X-Requested-With': 'XMLHttpRequest',
                'X-Forwarded-For': "*************",  
            }

        session = requests.Session()
        session.cookies.update(cookies)
        session.headers.update(headers)
        return session


    def clean_filename(filename):
        """清理文件名中的特殊字符"""
        return str(filename).replace('/', '_').replace('\\', '_').replace(':', '_').replace('?', '_').replace('*', '_').replace('"', '_').replace('<', '_').replace('>', '_').replace('|', '_')


    def save_json_file(url_info, json_path, filename_base):
        """保存JSON文件"""
        try:
            json_filepath = os.path.join(json_path, f"{filename_base}.json")
            with open(json_filepath, 'w', encoding='utf-8') as f:
                json.dump(url_info, f, ensure_ascii=False, indent=2)
            print(f"✓ JSON已保存: {filename_base}.json")
            return True
        except Exception as e:
            print(f"✗ JSON保存失败: {e}")
            return False


    def download_webpage_simple(session, url, web_path, filename_base):
        """下载网页内容（简化版，只保存HTML）"""
        try:
            response = session.get(url, timeout=30)
            response.raise_for_status()
            response.encoding = response.apparent_encoding or 'utf-8'

            html_filepath = os.path.join(web_path, f"{filename_base}.html")
            with open(html_filepath, 'w', encoding='utf-8') as f:
                f.write(response.text)

            print(f"✓ 网页已保存: {filename_base}.html")
            return True
        except Exception as e:
            print(f"✗ 网页下载失败: {e}")
            return False

    def download_webpage_with_assets(session, url, web_path, filename_base, attach_path):
        """下载网页内容及其图片资源和附件"""
        try:
            response = session.get(url, timeout=30)
            # 下载主页面
            response.raise_for_status()
            response.encoding = response.apparent_encoding or 'utf-8'

            # 解析HTML
            soup = BeautifulSoup(response.text, 'html.parser')

            # 检查并下载附件
            base_url = "https://apps.r93535.com"
            attachment_links = []

            # 查找所有包含下载路径的链接
            for link in soup.find_all('a', href=True):
                href = link.get('href')
                if href and '/cqdzyb/wtf/download?path=' in href:
                    # 检查path参数是否有值
                    path_param = href.split('path=', 1)[
                        1] if 'path=' in href else ''
                    if path_param.strip():  # 确保path参数不为空且不只是空白字符
                        attachment_links.append({
                            'element': link,
                            'href': href,
                            'text': link.get_text(strip=True)
                        })

            # 下载找到的附件
            if attachment_links:
                print(f"✓ 发现 {len(attachment_links)} 个附件链接")

                with tqdm(total=len(attachment_links), desc="下载附件", unit="个", leave=False) as pbar:
                    for i, attachment in enumerate(attachment_links):
                        try:
                            # 构造完整的下载URL
                            attach_url = urljoin(base_url, attachment['href'])

                            # 使用链接文本作为文件名的一部分，如果为空则使用默认名称
                            link_text = f"attachment_{i+1}"
                            # 清理文件名中的非法字符
                            safe_filename = "".join(
                                c for c in link_text if c.isalnum() or c in (' ', '-', '_', '.')).rstrip()
                            if not safe_filename:
                                safe_filename = f"attachment_{i+1}"

                            attachment_filename_base = f"{filename_base}_{safe_filename}"

                            # 调用下载附件函数
                            success = download_raw_file(
                                session, attach_url, attach_path, attachment_filename_base)

                            if success:
                                print(f"  ✓ 附件下载成功: {safe_filename}")
                            else:
                                print(f"  ✗ 附件下载失败: {safe_filename}")

                        except Exception as e:
                            print(f"  ✗ 附件下载异常: {attachment['text']} - {e}")

                        pbar.update(1)
                        time.sleep(0.1)  # 避免请求过于频繁
            else:
                print("✓ 未发现可下载的附件")

            # 下载图片资源
            img_tags = soup.find_all('img')
            if img_tags:
                # 创建资源目录
                assets_dir = os.path.join(web_path, f"{filename_base}_assets")
                os.makedirs(assets_dir, exist_ok=True)

                # 下载图片
                with tqdm(total=len(img_tags), desc=f"下载图片", unit="张", leave=False) as pbar:
                    for i, img in enumerate(img_tags):
                        src = img.get('src')
                        if src:
                            try:
                                img_url = urljoin(url, src)
                                parsed_url = urlparse(img_url)
                                ext = os.path.splitext(parsed_url.path)[
                                    1] or '.jpg'

                                img_filename = f"img_{i:03d}{ext}"
                                img_filepath = os.path.join(
                                    assets_dir, img_filename)

                                img_response = session.get(img_url, timeout=15)
                                img_response.raise_for_status()

                                with open(img_filepath, 'wb') as f:
                                    f.write(img_response.content)

                                # 更新HTML中的图片路径
                                img['src'] = f"{filename_base}_assets/{img_filename}"

                            except Exception as e:
                                print(f"\n  ✗ 图片下载失败: {e}")

                            pbar.update(1)
                            time.sleep(0.05)

            # 保存更新后的HTML
            html_filepath = os.path.join(web_path, f"{filename_base}.html")
            with open(html_filepath, 'w', encoding='utf-8') as f:
                f.write(str(soup))

            attachment_count = len(attachment_links) if attachment_links else 0
            print(
                f"✓ 网页已保存: {filename_base}.html (含 {len(img_tags)} 张图片, {attachment_count} 个附件)")
            return True

        except Exception as e:
            print(f"✗ 网页下载失败: {e}")
            return False


    def get_filename_from_url(url, session):
        """从URL或响应头获取原始文件名"""
        try:
            # 尝试从URL路径获取文件名
            parsed_url = urlparse(url)
            filename_from_url = os.path.basename(parsed_url.path)

            # 如果URL中没有明显的文件名，尝试从响应头获取
            if not filename_from_url or '.' not in filename_from_url:
                head_response = session.head(url, timeout=10)
                content_disposition = head_response.headers.get(
                    'content-disposition', '')
                if 'filename=' in content_disposition:
                    filename_from_header = content_disposition.split('filename=')[
                        1].strip('"')
                    return filename_from_header

            return filename_from_url if filename_from_url else 'downloaded_file'

        except Exception as e:
            print(f"获取文件名失败: {e}")
            return 'downloaded_file'


    def download_raw_file(session, url, raw_path, dks_name):
        """下载原始文件，保持原有格式"""
        try:
            # 获取原始文件名
            original_filename = get_filename_from_url(url, session)

            # 如果获取不到原始文件名，使用dks作为文件名
            if original_filename == 'downloaded_file':
                original_filename = dks_name
            else:
                # 检查文件扩展名
                file_extension = os.path.splitext(original_filename)[
                    1]  # 获取扩展名（包含点号）
                if file_extension:
                    # 如果有扩展名，使用dks_name + 扩展名
                    original_filename = f"{dks_name}{file_extension}"
                else:
                    # 如果没有扩展名，直接使用dks_name
                    original_filename = dks_name

            # 构建完整的保存路径
            raw_filepath = os.path.join(raw_path, original_filename)

            # 获取文件大小
            try:
                head_response = session.head(url, timeout=10)
                file_size = int(head_response.headers.get('content-length', 0))
            except:
                file_size = 0

            # 下载文件
            response = session.get(url, timeout=30, stream=True)
            response.raise_for_status()

            # 如果head请求失败，尝试从下载响应获取文件大小
            if file_size == 0:
                file_size = int(response.headers.get('content-length', 0))

            with open(raw_filepath, 'wb') as f:
                if file_size > 0:
                    with tqdm(total=file_size, desc=f"下载文件", unit="B", unit_scale=True, leave=False) as pbar:
                        for chunk in response.iter_content(chunk_size=8192):
                            if chunk:
                                f.write(chunk)
                                pbar.update(len(chunk))
                else:
                    # 无法获取文件大小时的下载方式
                    downloaded = 0
                    with tqdm(desc=f"下载文件", unit="KB", leave=False) as pbar:
                        for chunk in response.iter_content(chunk_size=8192):
                            if chunk:
                                f.write(chunk)
                                downloaded += len(chunk)
                                pbar.set_postfix(size=f"{downloaded/1024:.1f}KB")
                                pbar.update()

            print(f"✓ 文件已下载: {original_filename}")
            return True, original_filename

        except Exception as e:
            print(f"✗ 文件下载失败: {e}")
            return False, None


    def download_target_content(target_urls_info, base_path, site_name, forecast_name, cookies, expiredtime, headers=None, download_images=True):
        """
        主下载函数

        Args:
            target_urls_info: URL信息列表
            base_path: 基础路径
            site_name: 站点名称
            forecast_name: 预测名称
            cookies: 请求cookies
            expiredtime: token过期时间
            headers: 请求头（可选）
            download_images: 是否下载网页中的图片（默认True）
            
        Returns:
            tuple: (最新的token, 最新的过期时间)
        """
        print(f"开始下载任务...")
        print(f"基础路径: {base_path}")
        print(f"站点名称: {site_name}")
        print(f"预测名称: {forecast_name}")
        print("-" * 50)

        # 创建目录结构
        web_path, raw_path, json_path, attach_path = create_directories(
            base_path, site_name, forecast_name)
        print(f"创建目录:")
        print(f"  网页目录: {web_path}")
        print(f"  原始文件目录: {raw_path}")
        print(f"  JSON目录: {json_path}")
        print(f"  JSON目录: {attach_path}")

        print("-" * 50)

        # 创建会话
        session = create_session(cookies, headers)

        # 初始化token跟踪变量
        token_updated = False
        latest_token = None
        latest_expiredtime = expiredtime

        # 开始下载
        total_items = len(target_urls_info)
        success_count = 0

        with tqdm(total=total_items, desc="总体进度", unit="项") as pbar_total:
            for i, url_info in enumerate(target_urls_info):
                file_id = url_info.get('file_id')
                dks = url_info.get('dks', f'unknown_{i}')
                dzdc_url = url_info.get('dzdc_url')
                download_url = url_info.get('download_url')

                # 清理文件名
                filename_base = clean_filename(dks)

                pbar_total.set_description(f"处理: {filename_base}")
                print(f"\n[{i+1}/{total_items}] 处理: {filename_base}")

                item_success = True



                # 2. 下载网页
                if dzdc_url:
                    if download_images:
                        if not download_webpage_with_assets(session, dzdc_url, web_path, filename_base, attach_path):
                            item_success = False
                    else:
                        if not download_webpage_simple(session, dzdc_url, web_path, filename_base):
                            item_success = False

                # 3. 下载原始文件
                if download_url:
                    success, downloaded_filename = download_raw_file(
                        session, download_url, raw_path, filename_base)
                    if not success:
                        item_success = False
                    else:
                        print(f"  原始文件保存为: {downloaded_filename}")
                        
                # 1. 保存JSON
                if not save_json_file(url_info, json_path, filename_base):
                    item_success = False

                if item_success:
                    success_count += 1
                    print(f"✓ {filename_base} 处理完成")
                else:
                    print(f"✗ {filename_base} 处理部分失败")

                pbar_total.update(1)
                time.sleep(0.1)  # 避免请求过于频繁

                # 每5个i执行一次token检查
                if (i + 1) % 3 == 0:
                    new2token, new_expiredtime = check_and_refresh_token(latest_expiredtime)
                    if new2token != 0:
                        print(f"更新后的token有效期: {new_expiredtime}")
                        # 更新会话的cookies
                        session.cookies.update({'CRBIMSSOJWT': new2token})
                        # 记录token已更新
                        token_updated = True
                        latest_token = new2token
                        latest_expiredtime = new_expiredtime
                # 每执行20个i避免请求过于频繁
                if (i + 1) % 20 == 0:
                    time.sleep(1)

        print("-" * 50)
        print(f"下载任务完成!")
        print(f"成功处理: {success_count}/{total_items} 项")
        print(f"文件保存在: {os.path.join(base_path, site_name, forecast_name)}")

        # 返回最新的token信息
        if token_updated:
            # 如果token在循环中被更新过，返回最新的token
            return latest_token, latest_expiredtime
        else:
            # 否则返回会话中的原始token
            return session.cookies.get('CRBIMSSOJWT'), expiredtime


    token, expiredtime = download_target_content(
        target_urls_info=target_urls_info_not_existing,
        base_path=base_path,
        site_name=site_name,
        forecast_name=forecast_name,
        cookies=cookies,
        headers=headers,
        expiredtime=expiredtime,
        download_images=True  # 设置为False可以只下载HTML不下载图片
    )

    print(f"信息: {stats_message} \n 初始总数{total} \n 构造总数{len(target_urls_info)}\n 过滤后下载总数{len(target_urls_info_not_existing)}")