#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试附件检查功能
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from DzybDownloader import DZYBCrawler, DownloadItem

def create_test_files(base_path: str, filename_base: str):
    """创建测试文件结构"""
    site_name = "test_site"
    forecast_name = "test_forecast"
    
    # 创建目录结构
    web_path = os.path.join(base_path, site_name, forecast_name, "web")
    raw_path = os.path.join(base_path, site_name, forecast_name, "raw")
    json_path = os.path.join(base_path, site_name, forecast_name, "json")
    attach_path = os.path.join(base_path, site_name, forecast_name, "attach")
    
    for path in [web_path, raw_path, json_path, attach_path]:
        os.makedirs(path, exist_ok=True)
    
    # 创建测试文件
    # JSON文件
    json_file = os.path.join(json_path, f"{filename_base}.json")
    with open(json_file, 'w', encoding='utf-8') as f:
        f.write('{"test": "data"}')
    
    # HTML文件
    html_file = os.path.join(web_path, f"{filename_base}.html")
    with open(html_file, 'w', encoding='utf-8') as f:
        f.write('<html><body>Test</body></html>')
    
    # RAW文件
    raw_file = os.path.join(raw_path, f"{filename_base}.pdf")
    with open(raw_file, 'wb') as f:
        f.write(b'test raw data')
    
    # 旧版本格式的附件文件
    old_attach_file = os.path.join(attach_path, f"{filename_base}_attachment_1.doc")
    with open(old_attach_file, 'wb') as f:
        f.write(b'old attachment data')
    
    # 新版本格式的附件文件
    new_attach_file = os.path.join(attach_path, f"{filename_base}_技术报告.pdf")
    with open(new_attach_file, 'wb') as f:
        f.write(b'new attachment data')
    
    return web_path, raw_path, json_path, attach_path

def test_attachment_check():
    """测试附件检查功能"""
    print("开始测试附件检查功能...")
    
    # 创建临时目录
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"使用临时目录: {temp_dir}")
        
        # 创建测试文件
        filename_base = "DK123.456"
        web_path, raw_path, json_path, attach_path = create_test_files(temp_dir, filename_base)
        
        # 创建爬虫实例
        crawler = DZYBCrawler(base_path=temp_dir, max_workers=1)
        
        # 测试附件检查功能
        print("\n1. 测试 _check_existing_attachments 方法:")
        has_attachments, attachment_files = crawler._check_existing_attachments(attach_path, filename_base)
        print(f"   是否有附件: {has_attachments}")
        print(f"   附件文件: {attachment_files}")
        
        # 创建测试的DownloadItem
        test_item = DownloadItem(
            file_id=12345,
            dks="DK123.456",
            monitor_date="2024-01-01",
            dzdc_url=None,  # 设为None避免实际网络请求
            download_url="https://test.com/download",
            filename_base=filename_base
        )
        
        print("\n2. 测试 is_item_complete 方法 (不检查附件):")
        completeness = crawler.is_item_complete(test_item, web_path, raw_path, json_path)
        print(f"   完整性检查结果: {completeness}")
        
        print("\n3. 测试 is_item_complete 方法 (包含附件检查，但dzdc_url为None):")
        completeness_with_attach = crawler.is_item_complete(test_item, web_path, raw_path, json_path, attach_path)
        print(f"   完整性检查结果: {completeness_with_attach}")
        
        # 测试不同的filename_base
        print("\n4. 测试不存在的文件:")
        test_item_missing = DownloadItem(
            file_id=99999,
            dks="DK999.999",
            monitor_date="2024-01-01",
            dzdc_url=None,
            download_url="https://test.com/download",
            filename_base="DK999.999"
        )
        
        completeness_missing = crawler.is_item_complete(test_item_missing, web_path, raw_path, json_path, attach_path)
        print(f"   不存在文件的检查结果: {completeness_missing}")
        
        print("\n5. 测试附件前缀匹配:")
        # 创建更多测试附件
        extra_attach1 = os.path.join(attach_path, f"{filename_base}_extra_file.txt")
        extra_attach2 = os.path.join(attach_path, f"{filename_base}_another_attachment.zip")
        
        with open(extra_attach1, 'w') as f:
            f.write('extra file')
        with open(extra_attach2, 'wb') as f:
            f.write(b'zip data')
        
        has_attachments_extra, attachment_files_extra = crawler._check_existing_attachments(attach_path, filename_base)
        print(f"   扩展测试 - 是否有附件: {has_attachments_extra}")
        print(f"   扩展测试 - 附件文件: {attachment_files_extra}")
        print(f"   附件数量: {len(attachment_files_extra)}")

if __name__ == "__main__":
    test_attachment_check()
    print("\n测试完成！")
