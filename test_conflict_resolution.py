#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试附件下载冲突解决机制
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from DzybDownloader import DZYBCrawler, DownloadItem

def create_partial_files(base_path: str, filename_base: str):
    """创建部分文件的测试场景（模拟HTML丢失但附件完整的情况）"""
    site_name = "test_site"
    forecast_name = "test_forecast"
    
    # 创建目录结构
    web_path = os.path.join(base_path, site_name, forecast_name, "web")
    raw_path = os.path.join(base_path, site_name, forecast_name, "raw")
    json_path = os.path.join(base_path, site_name, forecast_name, "json")
    attach_path = os.path.join(base_path, site_name, forecast_name, "attach")
    
    for path in [web_path, raw_path, json_path, attach_path]:
        os.makedirs(path, exist_ok=True)
    
    # 创建JSON文件（完整）
    json_file = os.path.join(json_path, f"{filename_base}.json")
    with open(json_file, 'w', encoding='utf-8') as f:
        f.write('{"test": "data"}')
    
    # 不创建HTML文件（模拟丢失）
    
    # 创建RAW文件（完整）
    raw_file = os.path.join(raw_path, f"{filename_base}.pdf")
    with open(raw_file, 'wb') as f:
        f.write(b'test raw data')
    
    # 创建已存在的附件文件（完整）
    attach_file1 = os.path.join(attach_path, f"{filename_base}_技术报告.pdf")
    with open(attach_file1, 'wb') as f:
        f.write(b'existing attachment data - 1024 bytes' + b'0' * 1000)  # 固定大小便于测试
    
    attach_file2 = os.path.join(attach_path, f"{filename_base}_attachment_1.doc")
    with open(attach_file2, 'wb') as f:
        f.write(b'existing old format attachment - 512 bytes' + b'0' * 470)  # 固定大小
    
    return web_path, raw_path, json_path, attach_path

def test_conflict_resolution():
    """测试冲突解决机制"""
    print("开始测试附件下载冲突解决机制...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"使用临时目录: {temp_dir}")
        
        filename_base = "DK123.456"
        web_path, raw_path, json_path, attach_path = create_partial_files(temp_dir, filename_base)
        
        # 创建爬虫实例
        crawler = DZYBCrawler(base_path=temp_dir, max_workers=1)
        
        print("\n1. 检查初始文件状态:")
        print(f"   JSON文件: {os.path.exists(os.path.join(json_path, f'{filename_base}.json'))}")
        print(f"   HTML文件: {os.path.exists(os.path.join(web_path, f'{filename_base}.html'))}")
        print(f"   RAW文件: {os.path.exists(os.path.join(raw_path, f'{filename_base}.pdf'))}")
        
        # 列出现有附件
        existing_attachments = [f for f in os.listdir(attach_path) if f.startswith(filename_base)]
        print(f"   现有附件: {existing_attachments}")
        
        print("\n2. 测试完整性检查:")
        test_item = DownloadItem(
            file_id=12345,
            dks="DK123.456",
            monitor_date="2024-01-01",
            dzdc_url="https://example.com/test",  # 设置URL以触发附件检查
            download_url="https://test.com/download",
            filename_base=filename_base
        )
        
        # 注意：这里会尝试访问网络，在实际测试中可能失败，但可以看到逻辑
        try:
            completeness = crawler.is_item_complete(test_item, web_path, raw_path, json_path, attach_path)
            print(f"   完整性检查结果: {completeness}")
        except Exception as e:
            print(f"   完整性检查异常（预期，因为URL不存在）: {e}")
        
        print("\n3. 测试文件名生成和冲突检测:")
        
        # 测试已存在文件的检测
        filename1, exists1 = crawler._generate_unique_attachment_filename(filename_base, "技术报告", 0, attach_path)
        print(f"   '技术报告' -> {filename1}, 已存在: {exists1}")
        
        filename2, exists2 = crawler._generate_unique_attachment_filename(filename_base, "", 0, attach_path)
        print(f"   空文本 -> {filename2}, 已存在: {exists2}")
        
        filename3, exists3 = crawler._generate_unique_attachment_filename(filename_base, "新附件", 0, attach_path)
        print(f"   '新附件' -> {filename3}, 已存在: {exists3}")
        
        print("\n4. 测试跳过下载检查:")
        
        # 测试应该跳过的情况（文件已存在且大小匹配）
        # 注意：这个测试需要真实的URL，在实际环境中可能失败
        try:
            should_skip1, existing_file1 = crawler._should_skip_attachment_download(
                f"{filename_base}_技术报告", "https://httpbin.org/bytes/1024", attach_path)
            print(f"   技术报告文件跳过检查: {should_skip1}, 文件: {existing_file1}")
        except Exception as e:
            print(f"   跳过检查异常（预期，网络问题）: {e}")
        
        print("\n5. 模拟重新下载场景:")
        print("   场景：HTML文件丢失，需要重新下载整个项目")
        print("   预期行为：")
        print("   - 检测到项目不完整（缺少HTML）")
        print("   - 重新下载时发现附件已存在")
        print("   - 通过文件大小比较决定是否跳过附件下载")
        print("   - 如果内容不同，使用新文件名避免覆盖")

def test_filename_generation_edge_cases():
    """测试文件名生成的边界情况"""
    print("\n开始测试文件名生成边界情况...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        attach_path = os.path.join(temp_dir, "attach")
        os.makedirs(attach_path, exist_ok=True)
        
        crawler = DZYBCrawler(base_path=temp_dir, max_workers=1)
        filename_base = "DK123.456"
        
        print("\n1. 测试多个相同链接文本的处理:")
        
        # 模拟多个相同名称的附件
        results = []
        for i in range(5):
            filename, exists = crawler._generate_unique_attachment_filename(filename_base, "报告", i, attach_path)
            results.append((filename, exists))
            print(f"   第{i+1}个'报告' -> {filename}, 已存在: {exists}")
            
            # 创建文件以模拟已下载
            test_file = os.path.join(attach_path, f"{filename}.pdf")
            with open(test_file, 'w') as f:
                f.write(f'test content {i}')
        
        print("\n2. 测试混合场景:")
        mixed_texts = ["报告", "", "报告", "图纸", ""]
        for i, text in enumerate(mixed_texts):
            filename, exists = crawler._generate_unique_attachment_filename(filename_base, text, i, attach_path)
            print(f"   第{i+1}个'{text}' -> {filename}, 已存在: {exists}")

if __name__ == "__main__":
    print("=" * 70)
    print("测试附件下载冲突解决机制")
    print("=" * 70)
    
    test_conflict_resolution()
    
    print("\n" + "=" * 70)
    print("测试文件名生成边界情况")
    print("=" * 70)
    
    test_filename_generation_edge_cases()
    
    print("\n" + "=" * 70)
    print("所有测试完成！")
    print("=" * 70)
