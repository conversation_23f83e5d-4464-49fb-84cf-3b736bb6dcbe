import requests
import json
import os
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
from tqdm import tqdm
import time
import rsa
from datetime import datetime, timedelta
import concurrent.futures
import threading
from typing import List, Dict, Optional, Tuple
import logging
from dataclasses import dataclass
from functools import wraps
import pickle
from pathlib import Path
import hashlib
import glob
import random
import math

# 配置常量
class Config:
    """配置常量类"""
    MAX_FILE_SIZE = 500 * 1024 * 1024  # 500MB
    DEFAULT_ROWS_LIMIT = 10000
    TOKEN_CHECK_INTERVAL = 100
    MAX_RETRY_ATTEMPTS = 3
    RETRY_BACKOFF_FACTOR = 1.5
    MIN_LOGIN_INTERVAL_SECONDS = 30
    TOKEN_REFRESH_THRESHOLD_MINUTES = 15
    BATCH_TOKEN_CHECK_INTERVAL = 300  # 5分钟
    
    # 文件下载相关配置
    LARGE_FILE_THRESHOLD = 10 * 1024 * 1024  # 10MB
    LARGE_FILE_CHUNK_DELAY = 0.001  # 大文件块延迟
    SMALL_FILE_CHUNK_DELAY = 0.0005  # 小文件块延迟

# 自定义异常类
class DZYBError(Exception):
    """DZYB爬虫基础异常类"""
    pass

class TokenError(DZYBError):
    """Token相关异常"""
    pass

class NetworkError(DZYBError):
    """网络请求异常"""
    pass

class DataError(DZYBError):
    """数据处理异常"""
    pass

class ConfigError(DZYBError):
    """配置错误异常"""
    pass

def retry_on_failure(max_attempts: int = Config.MAX_RETRY_ATTEMPTS, 
                    backoff_factor: float = Config.RETRY_BACKOFF_FACTOR,
                    exceptions: tuple = (requests.exceptions.RequestException,)):
    """网络请求重试装饰器
    
    Args:
        max_attempts: 最大重试次数
        backoff_factor: 退避因子
        exceptions: 需要重试的异常类型
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None
            for attempt in range(max_attempts):
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    if attempt < max_attempts - 1:
                        delay = backoff_factor ** attempt
                        jitter = random.uniform(0.1, 0.5)  # 添加抖动避免同时重试
                        sleep_time = delay + jitter
                        logger.warning(f"请求失败，{sleep_time:.1f}秒后重试 (第{attempt + 1}/{max_attempts}次): {e}")
                        time.sleep(sleep_time)
                    else:
                        logger.error(f"请求失败，已达最大重试次数: {e}")
            raise last_exception
        return wrapper
    return decorator

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('dzyb_crawler.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class HumanBehaviorSimulator:
    """模拟人类浏览行为"""
    
    def __init__(self):
        self.reading_speed_range = (2, 8)  # 阅读速度范围（秒/页）
        self.mouse_movement_range = (0.5, 3)  # 鼠标移动间隔
        self.thinking_time_range = (1, 5)  # 思考时间
        self.typing_speed_range = (0.1, 0.3)  # 打字速度（秒/字符）
        
    def get_realistic_delay(self, action_type: str) -> float:
        """获取符合人类行为的延迟时间"""
        delays = {
            'page_load': random.uniform(2, 6),
            'form_submit': random.uniform(1, 3),
            'link_click': random.uniform(0.5, 2),
            'file_download': random.uniform(3, 8),
            'batch_operation': random.uniform(5, 15),
            'search_query': random.uniform(1, 4),
            'token_refresh': random.uniform(0.5, 2)
        }
        base_delay = delays.get(action_type, 1)
        
        # 添加随机波动
        variation = random.uniform(0.8, 1.2)
        return base_delay * variation
    
    def simulate_thinking_time(self) -> float:
        """模拟思考时间"""
        return random.uniform(*self.thinking_time_range)
    
    def simulate_reading_time(self, content_length: int) -> float:
        """模拟阅读时间"""
        # 假设阅读速度为每分钟200-400字
        reading_speed = random.randint(200, 400)
        base_time = content_length / reading_speed * 60  # 转换为秒
        return base_time * random.uniform(0.8, 1.2)

class SmartRequestScheduler:
    """智能请求调度器，避免规律性请求"""
    
    def __init__(self):
        self.request_history = []
        self.base_delay = 2
        self.max_concurrent_per_domain = 3
        self.domain_request_times = {}
        self.request_patterns = {
            'conservative': {'min_delay': 3, 'max_delay': 8, 'burst_limit': 2},
            'normal': {'min_delay': 1, 'max_delay': 4, 'burst_limit': 5},
            'aggressive': {'min_delay': 0.5, 'max_delay': 2, 'burst_limit': 8}
        }
        self.current_pattern = 'normal'
        
    def get_next_delay(self, domain: str = None) -> float:
        """获取下一个请求的延迟时间"""
        current_time = time.time()
        
        # 检查域名请求频率
        domain_key = domain or 'default'
        if domain_key not in self.domain_request_times:
            self.domain_request_times[domain_key] = []
        
        # 清理过期的请求记录（5分钟前）
        cutoff_time = current_time - 300
        self.domain_request_times[domain_key] = [
            t for t in self.domain_request_times[domain_key] if t > cutoff_time
        ]
        
        # 获取当前模式配置
        pattern = self.request_patterns[self.current_pattern]
        recent_requests = len(self.domain_request_times[domain_key])
        
        # 根据最近请求次数调整延迟
        if recent_requests >= pattern['burst_limit'] * 2:
            # 超出突发限制，大幅增加延迟
            delay = random.uniform(pattern['max_delay'] * 2, pattern['max_delay'] * 3)
        elif recent_requests >= pattern['burst_limit']:
            # 接近突发限制，增加延迟
            delay = random.uniform(pattern['max_delay'], pattern['max_delay'] * 1.5)
        else:
            # 正常延迟
            delay = random.uniform(pattern['min_delay'], pattern['max_delay'])
        
        # 添加随机波动
        delay *= random.uniform(0.8, 1.2)
        
        # 记录此次请求
        self.domain_request_times[domain_key].append(current_time)
        
        return delay
    
    def switch_pattern(self, pattern: str):
        """切换请求模式"""
        if pattern in self.request_patterns:
            self.current_pattern = pattern
            logger.info(f"请求模式切换为: {pattern}")
    
    def get_random_pattern(self) -> str:
        """随机选择请求模式"""
        return random.choice(list(self.request_patterns.keys()))

class AdvancedAntiDetection:
    """高级反检测技术"""
    
    def __init__(self):
        self.selenium_patterns = [
            'webdriver', 'selenium', 'phantomjs', 'headless',
            'automation', 'bot', 'crawler', 'spider'
        ]
        
    def sanitize_headers(self, headers: Dict) -> Dict:
        """清理请求头中的自动化特征"""
        sanitized = headers.copy()
        
        # 移除可疑的请求头
        suspicious_headers = [
            'x-selenium', 'x-automation', 'x-bot', 'x-crawler'
        ]
        
        for header in suspicious_headers:
            if header.lower() in [h.lower() for h in sanitized.keys()]:
                # 找到并删除可疑请求头
                keys_to_delete = [k for k in sanitized.keys() if k.lower() == header.lower()]
                for key in keys_to_delete:
                    del sanitized[key]
        
        return sanitized
    
    def generate_realistic_timing(self) -> Dict:
        """生成真实的时间相关参数"""
        return {
            'page_load_time': random.uniform(0.8, 3.2),
            'dom_ready_time': random.uniform(0.3, 1.5),
            'resource_load_time': random.uniform(0.2, 2.0),
            'render_time': random.uniform(0.1, 0.8)
        }
    
    def simulate_browser_fingerprint(self) -> Dict:
        """模拟浏览器指纹"""
        return {
            'screen_resolution': random.choice(['1920x1080', '1366x768', '1440x900', '1536x864']),
            'color_depth': random.choice([24, 32]),
            'pixel_ratio': random.choice([1, 1.25, 1.5, 2]),
            'hardware_concurrency': random.choice([2, 4, 6, 8, 12, 16]),
            'device_memory': random.choice([2, 4, 8, 16]),
            'platform': random.choice(['Win32', 'Win64', 'MacIntel', 'Linux x86_64'])
        }

@dataclass
class ParameterGroup:
    """参数组数据类"""
    site_id: int
    site_name: str
    forecast_method: int
    forecast_name: str
    
    def __str__(self):
        return f"Site: {self.site_name} (ID: {self.site_id}), Method: {self.forecast_name} ({self.forecast_method})"

@dataclass
class DownloadItem:
    """下载项数据类"""
    file_id: int
    dks: Optional[str]
    monitor_date: Optional[str]
    dzdc_url: Optional[str]
    download_url: str
    filename_base: str

class TokenCache:
    """Token缓存管理器，提供持久化和自动过期检查"""
    
    def __init__(self, cache_dir: str = ".token_cache"):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        self._cache_file = self.cache_dir / "dzyb_token.pkl"
        self._lock = threading.Lock()
        self._token_data = None
        # 统一的过期时间阈值
        self.TOKEN_REFRESH_THRESHOLD = timedelta(minutes=Config.TOKEN_REFRESH_THRESHOLD_MINUTES)  # 与TokenManager保持一致
    
    def _get_cache_key(self, appname: str, account: str) -> str:
        """生成缓存键"""
        key_string = f"{appname}:{account}"
        return hashlib.md5(key_string.encode()).hexdigest()
    
    def load(self, appname: str, account: str) -> Optional[Dict]:
        """加载token缓存"""
        with self._lock:
            cache_key = self._get_cache_key(appname, account)
            cache_file = self.cache_dir / f"{cache_key}.pkl"
            
            if not cache_file.exists():
                return None
            
            try:
                with open(cache_file, 'rb') as f:
                    data = pickle.load(f)
                    
                # 检查是否过期
                expires_at = datetime.fromisoformat(data['expires_at'])
                current_time = datetime.now()
                total_lifetime = expires_at - current_time
                if total_lifetime.total_seconds() <= 0:
                    refresh_threshold = expires_at - timedelta(minutes=Config.TOKEN_REFRESH_THRESHOLD_MINUTES)  # 降级到固定阈值
                else:
                    dynamic_threshold = total_lifetime * 0.15  # 15%的缓冲时间
                    dynamic_threshold = max(timedelta(minutes=5), min(dynamic_threshold, timedelta(minutes=30)))
                    refresh_threshold = expires_at - dynamic_threshold
                
                if current_time >= refresh_threshold:
                    logger.info(f"Token缓存已过期，删除缓存文件")
                    cache_file.unlink()
                    return None
                
                logger.info(f"从缓存加载有效Token，过期时间: {data['expires_at']}")
                return data
                
            except Exception as e:
                logger.warning(f"加载token缓存失败: {e}")
                if cache_file.exists():
                    cache_file.unlink()
                return None
    
    def save(self, appname: str, account: str, token: str, expires_at: datetime):
        """保存token缓存"""
        with self._lock:
            cache_key = self._get_cache_key(appname, account)
            cache_file = self.cache_dir / f"{cache_key}.pkl"
            
            data = {
                'token': token,
                'expires_at': expires_at.isoformat(),
                'created_at': datetime.now().isoformat(),
                'appname': appname,
                'account': account
            }
            
            try:
                with open(cache_file, 'wb') as f:
                    pickle.dump(data, f)
                logger.info(f"Token已缓存，过期时间: {expires_at}")
            except Exception as e:
                logger.error(f"保存token缓存失败: {e}")
    
    def clear(self, appname: str = None, account: str = None):
        """清除缓存"""
        with self._lock:
            if appname and account:
                cache_key = self._get_cache_key(appname, account)
                cache_file = self.cache_dir / f"{cache_key}.pkl"
                if cache_file.exists():
                    cache_file.unlink()
            else:
                for cache_file in self.cache_dir.glob("*.pkl"):
                    cache_file.unlink()

class HeaderManager:
    """请求头管理器，提供随机化和一致性管理"""
    
    def __init__(self):
        # 扩展的用户代理池
        self.user_agents = [
            # Chrome 最新版本
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36',
            # Firefox 最新版本
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:125.0) Gecko/20100101 Firefox/125.0',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:124.0) Gecko/20100101 Firefox/124.0',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:123.0) Gecko/20100101 Firefox/123.0',
            # Edge 最新版本
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36 Edg/135.0.0.0',
            # Safari 最新版本
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.1.15',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.1.15',
            # Linux 版本
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64; rv:125.0) Gecko/20100101 Firefox/125.0',
            # 移动设备版本
            'Mozilla/5.0 (iPhone; CPU iPhone OS 17_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Mobile/15E148 Safari/604.1',
            'Mozilla/5.0 (iPad; CPU OS 17_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Mobile/15E148 Safari/604.1',
        ]
        
        # 初始化增强组件
        self.behavior_simulator = HumanBehaviorSimulator()
        self.request_scheduler = SmartRequestScheduler()
        self.anti_detection = AdvancedAntiDetection()
        
        # 浏览器相关配置
        self.accept_languages = [
            'zh-CN,zh;q=0.9,en;q=0.8',
            'zh-CN,zh;q=0.8,en;q=0.7',
            'zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7',
            'en-US,en;q=0.9,zh-CN;q=0.8',
        ]
        
        self.accept_encodings = [
            'gzip, deflate, br',
            'gzip, deflate',
            'br, gzip, deflate',
        ]
        
        self.dnt_values = [None, '0', '1']  # Do Not Track 值
        
        # 请求模式历史
        self.request_history = []
        self.last_request_time = 0
        
        # 多样化的IP池，包含不同运营商和地区
        self.ip_pools = {
            "telecom": [
                "101.207", "112.65", "58.34", "222.92", 
                "180.163", "114.80", "116.231", "61.152",
                "218.1", "210.51", "202.96", "61.128"
            ],
            "unicom": [
                "61.135", "123.125", "111.202", "210.22",
                "218.30", "124.127", "111.161", "123.117",
                "112.64", "58.16", "210.13", "218.106"
            ],
            "mobile": [
                "117.136", "111.13", "120.196", "183.221",
                "120.239", "223.104", "111.9", "120.193",
                "112.17", "223.166", "120.194", "112.12"
            ]
        }
        
        # 基于地理位置的IP分布
        self.geo_ip_pools = {
            "北京": ["111.202", "123.125", "61.135", "111.161", "123.117"],
            "上海": ["112.65", "58.34", "222.92", "180.163", "114.80"],
            "广州": ["183.221", "120.196", "120.239", "112.17", "223.166"],
            "深圳": ["117.136", "111.13", "223.104", "111.9", "120.194"],
            "杭州": ["112.64", "218.1", "210.51", "202.96", "61.128"],
            "成都": ["118.112", "218.6", "182.150", "125.64", "171.208"]
        }
        
        # 为每个线程维护一致的User-Agent和IP
        self._thread_headers = {}
        self._thread_ip_timestamp = {}
        self._thread_current_network = {}
        self._lock = threading.Lock()
    
    def get_thread_headers(self, thread_id: Optional[int] = None) -> Dict[str, str]:
        """获取线程一致的请求头（增强版）"""
        if thread_id is None:
            thread_id = threading.get_ident()
        
        with self._lock:
            if thread_id not in self._thread_headers:
                # 为新线程分配随机的User-Agent和IP
                user_agent = random.choice(self.user_agents)
                x_forwarded_for = self._generate_realistic_ip(thread_id)
                
                # 生成更完整的浏览器指纹
                browser_fingerprint = self.anti_detection.simulate_browser_fingerprint()
                
                self._thread_headers[thread_id] = {
                    'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
                    'User-Agent': user_agent,
                    'Accept': 'application/json, text/javascript, */*; q=0.01',
                    'Accept-Language': random.choice(self.accept_languages),
                    'Accept-Encoding': random.choice(self.accept_encodings),
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-Forwarded-For': x_forwarded_for,
                    'Cache-Control': 'max-age=0',
                    'Sec-Ch-Ua': '"Chromium";v="136", "Google Chrome";v="136", "Not-A.Brand";v="24"',
                    'Sec-Ch-Ua-Mobile': '?0',
                    'Sec-Ch-Ua-Platform': '"Windows"',
                    'Sec-Fetch-Dest': 'document',
                    'Sec-Fetch-Mode': 'navigate',
                    'Sec-Fetch-Site': 'none',
                    'Sec-Fetch-User': '?1',
                    'Upgrade-Insecure-Requests': '1',
                }
                
                # 随机添加 DNT 头
                dnt_value = random.choice(self.dnt_values)
                if dnt_value:
                    self._thread_headers[thread_id]['DNT'] = dnt_value
            
            return self._thread_headers[thread_id].copy()
    
    def get_enhanced_headers(self, thread_id: Optional[int] = None, 
                           content_type: str = 'application/x-www-form-urlencoded; charset=UTF-8',
                           accept_type: str = 'application/json, text/javascript, */*; q=0.01') -> Dict[str, str]:
        """获取增强的请求头，根据不同请求类型调整"""
        headers = self.get_thread_headers(thread_id)
        
        # 根据请求类型调整 Content-Type 和 Accept
        headers['Content-Type'] = content_type
        headers['Accept'] = accept_type
        
        # 添加随机的时间戳，避免请求过于规律
        if random.random() < 0.3:  # 30% 概率添加
            headers['X-Timestamp'] = str(int(time.time() * 1000))
        
        # 清理可能的可疑特征
        headers = self.anti_detection.sanitize_headers(headers)
        
        return headers
    
    def get_headers_with_referer(self, referer_url: str, thread_id: Optional[int] = None) -> Dict[str, str]:
        """获取包含Referer的请求头"""
        headers = self.get_thread_headers(thread_id)
        headers['Referer'] = referer_url
        return headers
    
    def _generate_realistic_ip(self, thread_id: int) -> str:
        """生成更真实的随机IP地址"""
        current_time = time.time()
        
        # 检查是否需要更换IP网段（每5分钟）
        if thread_id not in self._thread_ip_timestamp:
            self._thread_ip_timestamp[thread_id] = current_time
            self._thread_current_network[thread_id] = self._select_network_by_strategy()
        elif current_time - self._thread_ip_timestamp[thread_id] > 300:  # 5分钟
            self._thread_current_network[thread_id] = self._select_network_by_strategy()
            self._thread_ip_timestamp[thread_id] = current_time
        
        network = self._thread_current_network[thread_id]
        third_octet = random.randint(1, 254)
        fourth_octet = random.randint(1, 254)
        
        return f"{network}.{third_octet}.{fourth_octet}"
    
    def _select_network_by_strategy(self) -> str:
        """选择IP网段的策略"""
        # 70%概率使用基于地理位置的IP，30%概率使用随机运营商IP
        if random.random() < 0.7:
            # 使用地理位置IP
            city = random.choice(list(self.geo_ip_pools.keys()))
            return random.choice(self.geo_ip_pools[city])
        else:
            # 使用随机运营商IP
            isp = random.choice(list(self.ip_pools.keys()))
            return random.choice(self.ip_pools[isp])
    
    def _generate_random_ip(self) -> str:
        """保留原方法名，用于向后兼容"""
        return self._generate_realistic_ip(threading.get_ident())
    
    def clear_thread_headers(self, thread_id: Optional[int] = None):
        """清除线程请求头缓存"""
        with self._lock:
            if thread_id is None:
                self._thread_headers.clear()
                self._thread_ip_timestamp.clear()
                self._thread_current_network.clear()
            else:
                if thread_id in self._thread_headers:
                    del self._thread_headers[thread_id]
                if thread_id in self._thread_ip_timestamp:
                    del self._thread_ip_timestamp[thread_id]
                if thread_id in self._thread_current_network:
                    del self._thread_current_network[thread_id]


class TokenManager:
    """现代化的Token管理器，使用装饰器模式和缓存机制"""
    
    # 类级别的锁，确保全局唯一性
    _global_lock = threading.Lock()
    _global_instance = None
    
    def __new__(cls, *args, **kwargs):
        """单例模式，确保全局只有一个TokenManager实例"""
        if cls._global_instance is None:
            with cls._global_lock:
                if cls._global_instance is None:
                    cls._global_instance = super().__new__(cls)
        return cls._global_instance
    
    def __init__(self, appname: str = None, account: str = None, password: str = None, config: Dict = None):
        # 防止重复初始化
        if hasattr(self, '_initialized') and self._initialized:
            return
            
        # 优先级：参数 > 配置文件 > 环境变量 > 默认值
        if config and 'credentials' in config:
            cred = config['credentials']
            self.appname = appname or cred.get('appname') or os.getenv('DZYB_APPNAME', 'yjjy')
            self.account = account or cred.get('account') or os.getenv('DZYB_ACCOUNT', 'czwlj')
            self.password = password or cred.get('password') or os.getenv('DZYB_PASSWORD', 'uFtxY2UoudZ57IdfVokl8Q==')
            public_key_from_config = cred.get('public_key')
        else:
            # 从环境变量或参数获取配置，避免硬编码敏感信息
            self.appname = appname or os.getenv('DZYB_APPNAME', 'yjjy')
            self.account = account or os.getenv('DZYB_ACCOUNT', 'czwlj')
            self.password = password or os.getenv('DZYB_PASSWORD', 'uFtxY2UoudZ57IdfVokl8Q==')
            public_key_from_config = None
        
        if not all([self.appname, self.account, self.password]):
            raise ConfigError("缺少必要的登录凭据，请在config.json中设置credentials或设置环境变量")
            
        self.token = None
        self.expired_time = None
        self.cache = TokenCache()
        self.header_manager = HeaderManager()
        
        # 从配置文件、环境变量或使用默认RSA公钥
        self.public_key_base64 = (
            public_key_from_config or 
            os.getenv('DZYB_PUBLIC_KEY') or
            "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCEJiVVdmEzgRk/Lqg+I8JyavmI6eRZCv9d3rJ/yDGUS0BbxrrUrhdK4t/hLZUjkjtCsX80eLxecn6HkMBHaEM0tQYmZyxpB5NBPHioggQHDrJVskzDdqliHobem+X54INPznlXXwLtRl4vh1/ducI7laD59bVg/I+h/SrepgOvqwIDAQAB"
        )
        self._login_semaphore = threading.Semaphore(1)  # 防止并发登录
        self._last_login_time = None
        self._min_login_interval = timedelta(seconds=Config.MIN_LOGIN_INTERVAL_SECONDS)  # 最小登录间隔
        self._global_token_lock = threading.RLock()  # 全局token读写锁
        
        # 延迟加载缓存，改为按需加载
        self._cache_loaded = False
        self._initialized = True
    
    def _load_from_cache(self):
        """从缓存加载token"""
        if self._cache_loaded:
            return  # 避免重复加载
            
        with self._global_token_lock:
            if self._cache_loaded:
                return  # 双重检查
                
            cached_data = self.cache.load(self.appname, self.account)
            if cached_data:
                self.token = cached_data['token']
                self.expired_time = cached_data['expires_at']
                logger.info(f"从缓存恢复Token，有效期至: {self.expired_time}")
            
            self._cache_loaded = True
    
    def encrypt_data(self, appname: str, account: str, password: str, timestamp: str) -> str:
        """加密登录数据"""
        pem_key = f"-----BEGIN PUBLIC KEY-----\n{self.public_key_base64}\n-----END PUBLIC KEY-----"
        public_key = rsa.PublicKey.load_pkcs1_openssl_pem(pem_key.encode())
        
        data = json.dumps({
            "timestamp": timestamp,
            "appname": appname,
            "password": password,
            "account": account
        })
        
        return rsa.encrypt(data.encode(), public_key).hex()
    
    @retry_on_failure(max_attempts=3, backoff_factor=2.0)
    def login(self, appname: str, account: str, password: str) -> Dict:
        """登录获取token"""
        # 使用信号量防止并发登录
        with self._login_semaphore:
            # 检查是否在最小登录间隔内
            if self._last_login_time and (datetime.now() - self._last_login_time) < self._min_login_interval:
                logger.warning("登录请求过于频繁，跳过")
                with self._global_token_lock:
                    return {"jwt": self.token or "0", "expiredtime": self.expired_time or ""}
            
            logger.info("正在执行登录...")
            timestamp = str(int(time.time() * 1000))
            token = self.encrypt_data(appname, account, password, timestamp)
            
            try:
                headers = self.header_manager.get_headers_with_referer(
                    'https://sso.r93535.com/api/v1.0/login'
                )
                response = requests.post(
                    'https://sso.r93535.com/api/v1.0/login',
                    data={
                        'appname': appname,
                        'account': account,
                        'password': password,
                        'timestamp': timestamp,
                        'token': token,
                        'realUrl': 'https://sso.r93535.com/api/v1.0/login'
                    },
                    headers=headers,
                    timeout=30
                )
                response.raise_for_status()
                login_result = response.json()
                
                # 更新最后登录时间
                self._last_login_time = datetime.now()
                
                # 更新token并缓存
                if 'jwt' in login_result and 'expiredtime' in login_result:
                    with self._global_token_lock:
                        self.token = login_result['jwt']
                        self.expired_time = login_result['expiredtime']
                        
                        # 解析过期时间并缓存
                        try:
                            expires_at = datetime.strptime(self.expired_time, '%Y-%m-%d %H:%M:%S')
                            self.cache.save(appname, account, self.token, expires_at)
                        except ValueError:
                            logger.warning("无法解析过期时间，不进行缓存")
                        
                        logger.info(f"登录成功，Token有效期至: {self.expired_time}")
                
                return login_result
                
            except requests.exceptions.Timeout as e:
                logger.error(f"登录请求超时: {e}")
                raise NetworkError(f"登录请求超时: {e}")
            except requests.exceptions.ConnectionError as e:
                logger.error(f"登录连接失败: {e}")
                raise NetworkError(f"登录连接失败: {e}")
            except requests.exceptions.HTTPError as e:
                logger.error(f"登录HTTP错误: {e}")
                raise NetworkError(f"登录HTTP错误: {e}")
            except requests.exceptions.RequestException as e:
                logger.error(f"登录请求异常: {e}")
                raise NetworkError(f"登录请求异常: {e}")
            except json.JSONDecodeError as e:
                logger.error(f"登录响应解析失败: {e}")
                raise DataError(f"登录响应解析失败: {e}")
            except Exception as e:
                logger.error(f"登录过程中发生未知错误: {e}")
                raise TokenError(f"登录过程中发生未知错误: {e}")
    
    def ensure_valid_token(self) -> bool:
        """确保token有效，如果无效则自动刷新"""
        with self._global_token_lock:
            # 如果没有token，尝试从缓存加载
            if not self.token:
                self._load_from_cache()
            
            if not self.token:
                logger.info("没有可用Token，执行登录")
                return self._force_refresh()
            
            # 检查token是否有效
            try:
                if not self.expired_time:
                    return self._force_refresh()
                
                # 尝试解析不同的时间格式
                try:
                    expired_time = datetime.strptime(self.expired_time, '%Y-%m-%d %H:%M:%S')
                except ValueError:
                    expired_time = datetime.fromisoformat(self.expired_time.replace('T', ' '))
                
                current_time = datetime.now()
                
                # 使用动态刷新阈值（过期时间的15%）
                # 计算token总有效期（假设token是现在生成的）
                total_lifetime = expired_time - current_time
                if total_lifetime.total_seconds() <= 0:
                    refresh_threshold = expired_time - timedelta(minutes=Config.TOKEN_REFRESH_THRESHOLD_MINUTES)  # 降级到固定阈值
                else:
                    dynamic_threshold = total_lifetime * 0.15  # 15%的缓冲时间
                    # 确保最小5分钟，最大30分钟的阈值
                    dynamic_threshold = max(timedelta(minutes=5), min(dynamic_threshold, timedelta(minutes=30)))
                    refresh_threshold = expired_time - dynamic_threshold
                
                if current_time >= refresh_threshold:
                    logger.info(f"Token即将过期，当前时间: {current_time.strftime('%Y-%m-%d %H:%M:%S')}, 过期时间: {self.expired_time}")
                    return self._force_refresh()
                else:
                    remaining_time = expired_time - current_time
                    logger.debug(f"Token仍然有效，剩余时间: {remaining_time}")
                    return True
                    
            except ValueError:
                logger.warning("时间格式解析失败，重新登录")
                return self._force_refresh()
    
    def _is_token_valid(self) -> bool:
        """快速检查token是否有效，不进行网络操作"""
        if not self.token or not self.expired_time:
            return False
        
        try:
            # 尝试解析不同的时间格式
            try:
                expired_time = datetime.strptime(self.expired_time, '%Y-%m-%d %H:%M:%S')
            except ValueError:
                # 如果失败，尝试ISO格式
                expired_time = datetime.fromisoformat(self.expired_time.replace('T', ' '))
            
            current_time = datetime.now()
            
            # 使用动态刷新阈值
            current_time = datetime.now()
            total_lifetime = expired_time - current_time
            if total_lifetime.total_seconds() <= 0:
                refresh_threshold = expired_time - timedelta(minutes=15)  # 降级到固定阈值
            else:
                dynamic_threshold = total_lifetime * 0.15  # 15%的缓冲时间
                dynamic_threshold = max(timedelta(minutes=5), min(dynamic_threshold, timedelta(minutes=30)))
                refresh_threshold = expired_time - dynamic_threshold
            
            return current_time < refresh_threshold
        except ValueError:
            return False
    
    def _force_refresh(self) -> bool:
        """强制刷新token"""
        logger.info("正在刷新Token...")
        try:
            login_result = self.login(self.appname, self.account, self.password)
            
            with self._global_token_lock:
                if login_result.get('jwt') and login_result.get('jwt') != "0":
                    self.token = login_result['jwt']
                    self.expired_time = login_result['expiredtime']
                    logger.info(f"Token刷新成功，有效期至: {self.expired_time}")
                    return True
                else:
                    logger.error("Token刷新失败：无效的登录响应")
                    return False
        except (NetworkError, TokenError, DataError) as e:
            logger.error(f"Token刷新失败: {e}")
            return False
        except Exception as e:
            logger.error(f"Token刷新时发生未知错误: {e}")
            return False
    
    def check_and_refresh_token(self, current_expiredtime: str) -> Tuple[str, str]:
        """检查token是否过期，必要时刷新（保持向后兼容）"""
        self.ensure_valid_token()
        return self.token or "0", self.expired_time or ""
    
    def auto_token_required(func):
        """装饰器：自动确保token有效"""
        @wraps(func)
        def wrapper(self, *args, **kwargs):
            if hasattr(self, 'token_manager'):
                self.token_manager.ensure_valid_token()
            return func(self, *args, **kwargs)
        return wrapper
    
    def get_cookies(self) -> Dict:
        """获取当前有效的cookies"""
        return {'CRBIMSSOJWT': self.token or ""}

class DZYBCrawler:
    """地质超前预报爬虫主类"""
    
    def __init__(self, base_path: str, max_workers: int = 5, config: Dict = None):
        self.base_path = base_path
        self.base_url = "https://apps.r93535.com"
        self.search_url = f"{self.base_url}/cqdzyb/site/DZTCinfo"
        self.token_manager = TokenManager(config=config)
        self.header_manager = HeaderManager()
        self.max_workers = max_workers
        self.session = self._create_session()
        
        # 延迟token检查，改为按需检查
        self._session_initialized = False
        
        # 增强的反爬虫组件
        self.behavior_simulator = self.header_manager.behavior_simulator
        self.request_scheduler = self.header_manager.request_scheduler
        self.anti_detection = self.header_manager.anti_detection
        
        # 请求统计和限制
        self.request_count = 0
        self.last_request_time = time.time()
        self.request_domain = "apps.r93535.com"
        
        # 随机切换请求模式
        self.pattern_switch_interval = random.randint(50, 150)  # 每50-150个请求切换模式
        self.request_count_since_pattern_switch = 0
        
        # 预报方法URL映射
        self.method_url_map = {
            7: ("/cqdzyb/dzdc/zzmsm", "掌子面素描"),
            1: ("/cqdzyb/wtf/tsp", "地震波反射"),
            4: ("/cqdzyb/wtf/dcb", "电磁波反射"),
            13: ("/cqdzyb/ztf/cqspzIndex", "超前水平钻"),
            14: ("/cqdzyb/ztf/jspk", "加深炮孔"),
            6: ("/cqdzyb/wtf/sbdc", "瞬变电磁"),
            8: ("/cqdzyb/dzdc/dsdzsm", "洞身素描"),
            2: ("/cqdzyb/wtf/hsp", "水平声波剖面"),
            9: ("/cqdzyb/wtf/wz", "微震监测"),
            0: (None, "其他")
        }
    
    def _create_session(self) -> requests.Session:
        """创建请求会话"""
        session = requests.Session()
        session.headers.update(self.header_manager.get_thread_headers())
        return session
    
    def _create_thread_session(self) -> requests.Session:
        """为线程创建独立的Session，避免并发冲突"""
        session = requests.Session()
        session.headers.update(self.header_manager.get_thread_headers())
        
        # 复制主session的cookies（token）
        if self.session.cookies:
            session.cookies.update(self.session.cookies)
        
        return session
    
    def _ensure_session_token(self):
        """确保session有有效的token，按需检查"""
        if not self._session_initialized:
            # 首次使用时检查token
            if self.token_manager.ensure_valid_token():
                self.session.cookies.update(self.token_manager.get_cookies())
                self._session_initialized = True
                logger.debug("Session token initialized")
        else:
            # 后续使用快速检查
            if not self.token_manager._is_token_valid():
                logger.debug("Token expired, refreshing...")
                if self.token_manager.ensure_valid_token():
                    self.session.cookies.update(self.token_manager.get_cookies())
    
    def batch_ensure_token(self, check_interval: int = Config.BATCH_TOKEN_CHECK_INTERVAL) -> bool:
        """批量token检查，带有检查间隔（默认5分钟）"""
        current_time = time.time()
        if not hasattr(self, '_last_token_check'):
            self._last_token_check = 0
            
        if current_time - self._last_token_check >= check_interval:
            self._last_token_check = current_time
            return self._ensure_session_token()
        return True
    
    def build_target_urls(self, file_id: int, forecast_method: int) -> Dict[str, str]:
        """构造完整的链接"""
        dzdc_path, method_name = self.method_url_map.get(forecast_method, (None, "未知"))
        
        dzdc_url = f"{self.base_url}{dzdc_path}?id={file_id}" if dzdc_path else None
        download_url = f"{self.base_url}/cqdzyb/site/download?id={file_id}&method={forecast_method}"
        
        return {
            "dzdc_url": dzdc_url,
            "download_url": download_url
        }
    
    def batch_build_urls(self, all_rows: List[Dict], forecast_method: int) -> List[DownloadItem]:
        """批量构建URL"""
        url_results = []
        
        for index, row in enumerate(all_rows):
            if not isinstance(row, dict):
                logger.warning(f"警告: 第{index}行不是字典类型: {row}")
                continue
            
            file_id = row.get('ID')
            dk = row.get('DKILO')
            m_date = row.get('MONITORDATE')
            
            if file_id is None:
                logger.warning(f"警告: 第{index}行缺少ID字段: {row}")
                continue
            
            try:
                urls = self.build_target_urls(file_id, forecast_method)
                filename_base = self._clean_filename(dk or f"unknown_{file_id}")
                
                url_results.append(DownloadItem(
                    file_id=file_id,
                    dks=dk,
                    monitor_date=m_date,
                    dzdc_url=urls['dzdc_url'],
                    download_url=urls['download_url'],
                    filename_base=filename_base
                ))
            except Exception as e:
                logger.error(f"警告: 构造file_id {file_id} 的URL时出错: {e}")
        
        return url_results
    
    def _clean_filename(self, filename: str) -> str:
        """清理文件名中的特殊字符"""
        return str(filename).replace('/', '_').replace('\\', '_').replace(':', '_').replace('?', '_').replace('*', '_').replace('"', '_').replace('<', '_').replace('>', '_').replace('|', '_')
    
    def get_total_count(self, site_id: int, forecast_method: int) -> int:
        """获取总记录数（已弃用，建议使用get_total_and_data）"""
        logger.warning("get_total_count已弃用，建议使用get_total_and_data")
        total, _ = self.get_total_and_data(site_id, forecast_method)
        return total
    
    def get_all_data(self, site_id: int, forecast_method: int, total: int) -> List[Dict]:
        """获取所有数据（已弃用，建议使用get_total_and_data）"""
        logger.warning("get_all_data已弃用，建议使用get_total_and_data")
        _, all_data = self.get_total_and_data(site_id, forecast_method)
        return all_data
    
    @retry_on_failure(max_attempts=3, backoff_factor=1.5)
    def get_total_and_data(self, site_id: int, forecast_method: int) -> Tuple[int, List[Dict]]:
        """一次性获取总数和所有数据，减少50%的网络请求（增强反爬虫版）"""
        # 确保token有效
        self._ensure_session_token()
        
        # 智能请求延迟
        delay = self.request_scheduler.get_next_delay(self.request_domain)
        time.sleep(delay)
        
        # 设置足够大的rows值获取所有数据
        payload = {
            'id': str(site_id),
            'method': str(forecast_method),
            'kwflag': '1',
            'page': '1',
            'rows': str(Config.DEFAULT_ROWS_LIMIT)  # 设置足够大的数值获取所有数据
        }
        
        headers = self.header_manager.get_headers_with_referer(
            f'{self.base_url}/cqdzyb/site/siteMin?id={site_id}&type=5'
        )
        
        # 模拟思考时间
        thinking_time = self.behavior_simulator.simulate_thinking_time()
        time.sleep(thinking_time)
        
        try:
            response = self.session.post(self.search_url, headers=headers, data=payload)
            response.raise_for_status()
            data = response.json()
            
            # 更新请求统计
            self._update_request_stats()
            
            return data['total'], data["rows"]
        except Exception as e:
            logger.error(f"获取数据失败: {e}")
            return 0, []
    
    def _update_request_stats(self):
        """更新请求统计"""
        self.request_count += 1
        self.last_request_time = time.time()
        self.request_count_since_pattern_switch += 1
        
        # 定期切换请求模式
        if self.request_count_since_pattern_switch >= self.pattern_switch_interval:
            new_pattern = self.request_scheduler.get_random_pattern()
            self.request_scheduler.switch_pattern(new_pattern)
            self.pattern_switch_interval = random.randint(50, 150)
            self.request_count_since_pattern_switch = 0
            logger.info(f"请求模式已切换为: {new_pattern}")
    
    def _get_smart_delay(self, request_type: str) -> float:
        """获取智能延迟时间"""
        base_delay = self.behavior_simulator.get_realistic_delay(request_type)
        scheduler_delay = self.request_scheduler.get_next_delay(self.request_domain)
        
        # 取两者中较大的值
        return max(base_delay, scheduler_delay)
    
    def _check_page_has_attachments(self, url: str, session: requests.Session) -> bool:
        """检查页面是否包含附件链接"""
        try:
            headers = self.header_manager.get_headers_with_referer(self.base_url)
            response = session.get(url, timeout=30, headers=headers)
            response.raise_for_status()
            response.encoding = response.apparent_encoding or 'utf-8'

            soup = BeautifulSoup(response.text, 'html.parser')

            # 查找所有包含下载路径的链接
            for link in soup.find_all('a', href=True):
                href = link.get('href')
                if href and '/cqdzyb/wtf/download?path=' in href:
                    # 检查path参数是否有值
                    path_param = href.split('path=', 1)[1] if 'path=' in href else ''
                    if path_param.strip():  # 确保path参数不为空且不只是空白字符
                        return True
            return False
        except Exception as e:
            logger.warning(f"检查页面附件时出错: {e}")
            return False

    def _check_existing_attachments(self, attach_path: str, filename_base: str) -> Tuple[bool, List[str]]:
        """检查本地是否存在附件文件（兼容新旧版本命名）"""
        if not os.path.exists(attach_path):
            return False, []

        try:
            # 使用filename_base前缀匹配所有相关附件文件
            # 这样可以兼容新旧版本的不同命名方式：
            # 旧版本: {filename_base}_attachment_1.ext
            # 新版本: {filename_base}_实际链接文本.ext 或 {filename_base}_attachment_1.ext
            attachment_pattern = os.path.join(attach_path, f"{filename_base}_*")
            existing_attachments = glob.glob(attachment_pattern)

            if existing_attachments:
                # 返回找到的附件文件名（仅文件名，不含路径）
                attachment_names = [os.path.basename(f) for f in existing_attachments]
                logger.debug(f"找到已存在的附件: {attachment_names}")
                return True, attachment_names
            else:
                return False, []

        except Exception as e:
            logger.warning(f"检查附件目录时出错: {e}")
            return False, []

    def _generate_unique_attachment_filename(self, filename_base: str, link_text: str, index: int, attach_path: str) -> str:
        """生成唯一的附件文件名，避免重名冲突

        Args:
            filename_base: 基础文件名
            link_text: 链接文本
            index: 附件索引（从0开始）
            attach_path: 附件目录路径

        Returns:
            唯一的附件文件名基础部分（不含扩展名）
        """
        # 首先尝试使用实际链接文本
        if link_text.strip():
            # 清理链接文本中的非法字符
            safe_text = "".join(c for c in link_text.strip() if c.isalnum() or c in (' ', '-', '_', '.')).rstrip()
            if safe_text:
                base_name = f"{filename_base}_{safe_text}"

                # 检查是否已存在同名文件（任何扩展名）
                pattern = os.path.join(attach_path, f"{base_name}.*")
                existing_files = glob.glob(pattern)

                if not existing_files:
                    # 没有同名文件，可以直接使用
                    logger.debug(f"使用链接文本作为附件名: {base_name}")
                    return base_name
                else:
                    # 存在同名文件，添加序号避免冲突
                    unique_name = f"{base_name}_{index + 1}"
                    logger.debug(f"检测到重名，使用序号: {unique_name} (原名: {base_name})")
                    return unique_name

        # 如果链接文本为空或无效，使用默认格式
        default_name = f"{filename_base}_attachment_{index + 1}"
        logger.debug(f"使用默认附件名: {default_name}")
        return default_name

    def is_item_complete(self, item: DownloadItem, web_path: str, raw_path: str, json_path: str, attach_path: str = None) -> Dict[str, bool]:
        """检查项目是否完整下载所有必需文件（增强版，支持附件检查）"""
        result = {
            'is_complete': False,
            'has_json': False,
            'has_html': False,
            'has_raw': False,
            'has_attachments': True,  # 默认为True，只有当页面有附件但本地没有时才为False
            'missing_files': [],
            'existing_attachments': []
        }

        # 检查JSON文件
        json_file = os.path.join(json_path, f"{item.filename_base}.json")
        result['has_json'] = os.path.exists(json_file)
        if not result['has_json']:
            result['missing_files'].append(f"JSON: {item.filename_base}.json")

        # 检查HTML文件（如果存在dzdc_url）
        if item.dzdc_url:
            html_file = os.path.join(web_path, f"{item.filename_base}.html")
            result['has_html'] = os.path.exists(html_file)
            if not result['has_html']:
                result['missing_files'].append(f"HTML: {item.filename_base}.html")
        else:
            result['has_html'] = True  # 如果没有HTML URL，认为已满足

        # 检查RAW文件（如果存在download_url）
        if item.download_url:
            # RAW文件可能有多种扩展名，检查匹配的文件
            raw_pattern = os.path.join(raw_path, f"{item.filename_base}.*")
            raw_files = glob.glob(raw_pattern)
            result['has_raw'] = len(raw_files) > 0
            if not result['has_raw']:
                result['missing_files'].append(f"RAW: {item.filename_base}.*")
        else:
            result['has_raw'] = True  # 如果没有RAW URL，认为已满足

        # 检查附件（只有当页面存在且有附件时才检查）
        if attach_path and item.dzdc_url:
            # 首先检查页面是否包含附件
            session = self._create_thread_session()
            page_has_attachments = self._check_page_has_attachments(item.dzdc_url, session)

            if page_has_attachments:
                # 页面有附件，检查本地是否存在
                has_local_attachments, existing_files = self._check_existing_attachments(attach_path, item.filename_base)
                result['has_attachments'] = has_local_attachments
                result['existing_attachments'] = existing_files

                if not has_local_attachments:
                    result['missing_files'].append(f"ATTACHMENTS: {item.filename_base}_*")
                else:
                    logger.debug(f"项目 {item.filename_base} 存在附件: {len(existing_files)} 个")
            else:
                # 页面没有附件，认为附件检查通过
                result['has_attachments'] = True
                logger.debug(f"项目 {item.filename_base} 页面无附件")

        # 判断是否完整（包含附件检查）
        result['is_complete'] = (result['has_json'] and result['has_html'] and
                               result['has_raw'] and result['has_attachments'])

        return result

    def filter_existing_data(self, target_urls_info: List[DownloadItem], site_name: str, forecast_name: str) -> Tuple[List[DownloadItem], str]:
        """检查本地数据并过滤已存在的目标数据项（增强版，支持附件检查）"""
        web_path = os.path.join(self.base_path, site_name, forecast_name, "web")
        raw_path = os.path.join(self.base_path, site_name, forecast_name, "raw")
        json_path = os.path.join(self.base_path, site_name, forecast_name, "json")
        attach_path = os.path.join(self.base_path, site_name, forecast_name, "attach")

        # 检查目录是否存在
        if not all(os.path.exists(path) for path in [web_path, raw_path, json_path]):
            return target_urls_info, "无本地数据目录，将下载所有目标数据"

        original_count = len(target_urls_info)
        filtered_data = []
        complete_count = 0
        partial_count = 0
        missing_count = 0
        attachment_check_count = 0

        logger.info(f"开始检查 {original_count} 个项目的完整性...")

        for i, item in enumerate(target_urls_info):
            if (i + 1) % 10 == 0:
                logger.info(f"已检查 {i + 1}/{original_count} 个项目...")

            completeness = self.is_item_complete(item, web_path, raw_path, json_path, attach_path)

            # 统计附件检查情况
            if len(completeness.get('existing_attachments', [])) > 0:
                attachment_check_count += 1

            if completeness['is_complete']:
                # 所有必需文件都存在（包括附件）
                complete_count += 1
                attachment_info = ""
                if completeness.get('existing_attachments'):
                    attachment_info = f" (含{len(completeness['existing_attachments'])}个附件)"
                logger.debug(f"项目完整，跳过: {item.filename_base}{attachment_info}")
            elif (completeness['has_json'] or completeness['has_html'] or
                  completeness['has_raw'] or completeness.get('existing_attachments')):
                # 部分文件存在，但不完整
                partial_count += 1
                logger.debug(f"项目不完整，重新下载: {item.filename_base} (缺失: {', '.join(completeness['missing_files'])})")
                filtered_data.append(item)
            else:
                # 没有任何文件存在
                missing_count += 1
                filtered_data.append(item)

        download_count = len(filtered_data)

        # 构建统计信息
        stats_parts = []
        stats_parts.append(f"总共{original_count}条数据")
        if complete_count > 0:
            stats_parts.append(f"{complete_count}条完整")
        if partial_count > 0:
            stats_parts.append(f"{partial_count}条不完整")
        if missing_count > 0:
            stats_parts.append(f"{missing_count}条缺失")
        if attachment_check_count > 0:
            stats_parts.append(f"{attachment_check_count}条含附件")

        if download_count == 0:
            stats_message = f"{', '.join(stats_parts)}，所有项目均完整，无需下载"
        else:
            stats_parts.append(f"即将下载{download_count}条")
            stats_message = f"{', '.join(stats_parts)}"

        logger.info(f"完整性检查完成: {stats_message}")
        return filtered_data, stats_message
    
    def create_directories(self, site_name: str, forecast_name: str) -> Tuple[str, str, str, str]:
        """创建所需的目录结构"""
        web_path = os.path.join(self.base_path, site_name, forecast_name, "web")
        raw_path = os.path.join(self.base_path, site_name, forecast_name, "raw")
        json_path = os.path.join(self.base_path, site_name, forecast_name, "json")
        attach_path = os.path.join(self.base_path, site_name, forecast_name, "attach")

        os.makedirs(web_path, exist_ok=True)
        os.makedirs(raw_path, exist_ok=True)
        os.makedirs(json_path, exist_ok=True)
        os.makedirs(attach_path, exist_ok=True)

        return web_path, raw_path, json_path, attach_path
    
    def save_json_file(self, url_info: DownloadItem, json_path: str) -> bool:
        """保存JSON文件"""
        try:
            json_filepath = os.path.join(json_path, f"{url_info.filename_base}.json")
            with open(json_filepath, 'w', encoding='utf-8') as f:
                json.dump({
                    'file_id': url_info.file_id,
                    'dks': url_info.dks,
                    'monitor_date': url_info.monitor_date,
                    'dzdc_url': url_info.dzdc_url,
                    'download_url': url_info.download_url
                }, f, ensure_ascii=False, indent=2)
            logger.info(f"✓ JSON已保存: {url_info.filename_base}.json")
            return True
        except Exception as e:
            logger.error(f"✗ JSON保存失败: {e}")
            return False
    
    def download_webpage_simple(self, url: str, web_path: str, filename_base: str) -> bool:
        """下载网页内容（简化版，只保存HTML）"""
        return self.download_webpage_simple_with_session(url, web_path, filename_base, self.session)
    
    @retry_on_failure(max_attempts=2, backoff_factor=1.2)
    def download_webpage_simple_with_session(self, url: str, web_path: str, filename_base: str, session: requests.Session) -> bool:
        """下载网页内容（使用指定Session，增强反爬虫版）"""
        try:
            # 智能延迟
            delay = self._get_smart_delay('page_load')
            time.sleep(delay)
            
            headers = self.header_manager.get_headers_with_referer(self.base_url)
            headers['Content-Type'] = 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8'
            headers['Accept'] = 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8'
            
            response = session.get(url, timeout=30, headers=headers)
            response.raise_for_status()
            response.encoding = response.apparent_encoding or 'utf-8'

            html_filepath = os.path.join(web_path, f"{filename_base}.html")
            with open(html_filepath, 'w', encoding='utf-8') as f:
                f.write(response.text)

            # 更新请求统计
            self._update_request_stats()
            
            logger.info(f"✓ 网页已保存: {filename_base}.html")
            return True
        except Exception as e:
            logger.error(f"✗ 网页下载失败: {e}")
            return False
    
    def download_webpage_with_assets(self, url: str, web_path: str, filename_base: str, attach_path: str) -> bool:
        """下载网页内容及其图片资源和附件"""
        return self.download_webpage_with_assets_with_session(url, web_path, filename_base, attach_path, self.session)
    
    def download_webpage_with_assets_with_session(self, url: str, web_path: str, filename_base: str, attach_path: str, session: requests.Session) -> bool:
        """下载网页内容及其图片资源和附件（使用指定Session）"""
        try:
            headers = self.header_manager.get_headers_with_referer(self.base_url)
            response = session.get(url, timeout=30, headers=headers)
            response.raise_for_status()
            response.encoding = response.apparent_encoding or 'utf-8'

            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 检查并下载附件
            attachment_links = []
            for link in soup.find_all('a', href=True):
                href = link.get('href')
                if href and '/cqdzyb/wtf/download?path=' in href:
                    path_param = href.split('path=', 1)[1] if 'path=' in href else ''
                    if path_param.strip():
                        attachment_links.append({
                            'element': link,
                            'href': href,
                            'text': link.get_text(strip=True)
                        })

            # 下载附件
            if attachment_links:
                logger.info(f"✓ 发现 {len(attachment_links)} 个附件链接")
                for i, attachment in enumerate(attachment_links):
                    try:
                        attach_url = urljoin(self.base_url, attachment['href'])
                        link_text = attachment['text'].strip()

                        # 使用新的唯一文件名生成方法，避免重名冲突
                        attachment_filename_base = self._generate_unique_attachment_filename(
                            filename_base, link_text, i, attach_path)

                        # 调用下载附件函数
                        success = self._download_attachment_direct_with_session(attach_url, attach_path, attachment_filename_base, session)

                        if success:
                            # 显示更友好的成功信息
                            display_name = link_text if link_text else f"attachment_{i+1}"
                            actual_filename = os.path.basename(attachment_filename_base)
                            if display_name != actual_filename:
                                logger.info(f"  ✓ 附件下载成功: {display_name} -> {actual_filename}")
                            else:
                                logger.info(f"  ✓ 附件下载成功: {actual_filename}")
                        else:
                            display_name = link_text if link_text else f"attachment_{i+1}"
                            logger.error(f"  ✗ 附件下载失败: {display_name}")

                    except Exception as e:
                        display_text = attachment.get('text', f'attachment_{i+1}')
                        logger.error(f"  ✗ 附件下载异常: {display_text} - {e}")

                    time.sleep(0.1)
            else:
                logger.info("✓ 未发现可下载的附件")

            # 下载图片资源
            img_tags = soup.find_all('img')
            if img_tags:
                assets_dir = os.path.join(web_path, f"{filename_base}_assets")
                os.makedirs(assets_dir, exist_ok=True)

                for i, img in enumerate(img_tags):
                    src = img.get('src')
                    if src:
                        try:
                            img_url = urljoin(url, src)
                            parsed_url = urlparse(img_url)
                            ext = os.path.splitext(parsed_url.path)[1] or '.jpg'

                            img_filename = f"img_{i:03d}{ext}"
                            img_filepath = os.path.join(assets_dir, img_filename)

                            img_response = session.get(img_url, timeout=15)
                            img_response.raise_for_status()

                            with open(img_filepath, 'wb') as f:
                                f.write(img_response.content)

                            img['src'] = f"{filename_base}_assets/{img_filename}"

                        except Exception as e:
                            logger.error(f"  ✗ 图片下载失败: {e}")

                        time.sleep(0.05)

            # 保存更新后的HTML
            html_filepath = os.path.join(web_path, f"{filename_base}.html")
            with open(html_filepath, 'w', encoding='utf-8') as f:
                f.write(str(soup))

            attachment_count = len(attachment_links) if attachment_links else 0
            logger.info(f"✓ 网页已保存: {filename_base}.html (含 {len(img_tags)} 张图片, {attachment_count} 个附件)")
            return True

        except Exception as e:
            logger.error(f"✗ 网页下载失败: {e}")
            return False
    
    def get_filename_from_url(self, url: str, head_response: Optional[requests.Response] = None) -> str:
        """从URL或响应头获取原始文件名（优化：支持传入head_response避免重复请求）"""
        try:
            parsed_url = urlparse(url)
            filename_from_url = os.path.basename(parsed_url.path)

            # 如果URL中没有明显的文件名，尝试从响应头获取
            if not filename_from_url or '.' not in filename_from_url:
                # 优化：如果已经传入了head_response，直接使用，避免重复请求
                if head_response:
                    content_disposition = head_response.headers.get('content-disposition', '')
                else:
                    head_response = self.session.head(url, timeout=10)
                    content_disposition = head_response.headers.get('content-disposition', '')
                
                if 'filename=' in content_disposition:
                    filename_from_header = content_disposition.split('filename=')[1].strip('"')
                    return filename_from_header

            return filename_from_url if filename_from_url else 'downloaded_file'

        except Exception as e:
            logger.error(f"获取文件名失败: {e}")
            return 'downloaded_file'
    
    def download_raw_file(self, url: str, raw_path: str, dks_name: str) -> Tuple[bool, Optional[str]]:
        """下载原始文件，保持原有格式（优化：消除重复HEAD请求）"""
        return self.download_raw_file_with_session(url, raw_path, dks_name, self.session)
    
    @retry_on_failure(max_attempts=2, backoff_factor=1.5)
    def download_raw_file_with_session(self, url: str, raw_path: str, dks_name: str, session: requests.Session) -> Tuple[bool, Optional[str]]:
        """下载原始文件，保持原有格式（使用指定Session，增强反爬虫版）"""
        try:
            # 智能延迟 - 文件下载通常需要更长的延迟
            delay = self._get_smart_delay('file_download')
            time.sleep(delay)
            
            # 优化：先发送HEAD请求获取文件信息和大小
            head_response = None
            file_size = 0
            try:
                # 使用增强的请求头
                headers = self.header_manager.get_headers_with_referer(self.base_url)
                headers['Accept'] = 'application/octet-stream,*/*'
                head_response = session.head(url, timeout=10, headers=headers)
                head_response.raise_for_status()
                file_size = int(head_response.headers.get('content-length', 0))
            except:
                # HEAD请求失败，不影响后续下载
                head_response = None
                file_size = 0

            # 获取原始文件名（复用HEAD响应）
            original_filename = self.get_filename_from_url(url, head_response)

            # 如果获取不到原始文件名，使用dks作为文件名
            if original_filename == 'downloaded_file':
                original_filename = dks_name
            else:
                # 检查文件扩展名
                file_extension = os.path.splitext(original_filename)[1]  # 获取扩展名（包含点号）
                if file_extension:
                    # 如果有扩展名，使用dks_name + 扩展名
                    original_filename = f"{dks_name}{file_extension}"
                else:
                    # 如果没有扩展名，直接使用dks_name
                    original_filename = dks_name

            # 构建完整的保存路径
            raw_filepath = os.path.join(raw_path, original_filename)

            # 下载文件
            headers = self.header_manager.get_headers_with_referer(self.base_url)
            headers['Accept'] = 'application/octet-stream,*/*'
            response = session.get(url, timeout=30, stream=True, headers=headers)
            response.raise_for_status()

            # 如果HEAD请求失败，尝试从下载响应获取文件大小
            if file_size == 0:
                file_size = int(response.headers.get('content-length', 0))

            # 文件下载进度
            with open(raw_filepath, 'wb') as f:
                if file_size > 0:
                    with tqdm(total=file_size, desc=f"下载文件", unit="B", unit_scale=True, leave=False) as pbar:
                        for chunk in response.iter_content(chunk_size=8192):
                            if chunk:
                                f.write(chunk)
                                pbar.update(len(chunk))
                                # 智能延迟策略：根据文件大小和网络状况动态调整
                                if file_size > Config.LARGE_FILE_THRESHOLD:
                                    # 大文件：根据文件大小比例调整延迟
                                    size_factor = min(file_size / Config.LARGE_FILE_THRESHOLD, 10)  # 最大10倍
                                    delay = Config.LARGE_FILE_CHUNK_DELAY * size_factor
                                    time.sleep(delay)
                else:
                    # 无法获取文件大小时的下载方式
                    downloaded = 0
                    with tqdm(desc=f"下载文件", unit="KB", leave=False) as pbar:
                        for chunk in response.iter_content(chunk_size=8192):
                            if chunk:
                                f.write(chunk)
                                downloaded += len(chunk)
                                pbar.set_postfix(size=f"{downloaded/1024:.1f}KB")
                                pbar.update()
                                # 小文件也添加微延迟，但更短
                                time.sleep(Config.SMALL_FILE_CHUNK_DELAY)

            # 更新请求统计
            self._update_request_stats()
            
            logger.info(f"✓ 文件已下载: {original_filename}")
            return True, original_filename

        except Exception as e:
            logger.error(f"✗ 文件下载失败: {e}")
            return False, None
    
    def _download_attachment_direct(self, url: str, attach_path: str, filename_base: str) -> bool:
        """直接下载附件，避免递归调用"""
        return self._download_attachment_direct_with_session(url, attach_path, filename_base, self.session)
    
    def _download_attachment_direct_with_session(self, url: str, attach_path: str, filename_base: str, session: requests.Session) -> bool:
        """直接下载附件，避免递归调用（使用指定Session）"""
        try:
            # 获取原始文件名
            original_filename = self.get_filename_from_url(url)
            
            # 如果获取不到原始文件名，使用filename_base作为文件名
            if original_filename == 'downloaded_file':
                original_filename = filename_base
            else:
                # 检查文件扩展名
                file_extension = os.path.splitext(original_filename)[1]  # 获取扩展名（包含点号）
                if file_extension:
                    # 如果有扩展名，使用filename_base + 扩展名
                    original_filename = f"{filename_base}{file_extension}"
                else:
                    # 如果没有扩展名，直接使用filename_base
                    original_filename = filename_base
            
            # 构建完整的保存路径
            attach_filepath = os.path.join(attach_path, original_filename)
            
            # 检查文件是否已存在
            if os.path.exists(attach_filepath):
                logger.info(f"  附件已存在，跳过: {original_filename}")
                return True
            
            # 获取文件大小
            try:
                headers = self.header_manager.get_headers_with_referer(self.base_url)
                head_response = session.head(url, timeout=10, headers=headers)
                file_size = int(head_response.headers.get('content-length', 0))
            except:
                file_size = 0
            
            # 限制附件大小
            max_size = Config.MAX_FILE_SIZE
            if file_size > max_size:
                logger.warning(f"  附件过大({file_size/1024/1024:.1f}MB)，跳过: {original_filename}")
                return True
            
            # 下载文件
            headers = self.header_manager.get_headers_with_referer(self.base_url)
            response = session.get(url, timeout=30, stream=True, headers=headers)
            response.raise_for_status()
            
            # 如果head请求失败，尝试从下载响应获取文件大小
            if file_size == 0:
                file_size = int(response.headers.get('content-length', 0))
            
            with open(attach_filepath, 'wb') as f:
                if file_size > 0:
                    with tqdm(total=file_size, desc=f"下载附件", unit="B", unit_scale=True, leave=False) as pbar:
                        for chunk in response.iter_content(chunk_size=8192):
                            if chunk:
                                f.write(chunk)
                                pbar.update(len(chunk))
                else:
                    downloaded = 0
                    with tqdm(desc=f"下载附件", unit="KB", leave=False) as pbar:
                        for chunk in response.iter_content(chunk_size=8192):
                            if chunk:
                                f.write(chunk)
                                downloaded += len(chunk)
                                pbar.set_postfix(size=f"{downloaded/1024:.1f}KB")
                                pbar.update()
            
            logger.info(f"  附件已下载: {original_filename}")
            return True
            
        except Exception as e:
            logger.error(f"  附件下载失败: {filename_base} - {e}")
            return False
    
    def download_single_item(self, item: DownloadItem, web_path: str, raw_path: str, json_path: str, attach_path: str, download_images: bool = True) -> bool:
        """下载单个项目（使用主Session）"""
        return self.download_single_item_with_session(item, web_path, raw_path, json_path, attach_path, download_images, self.session)
    
    def download_single_item_with_session(self, item: DownloadItem, web_path: str, raw_path: str, json_path: str, attach_path: str, download_images: bool = True, session: requests.Session = None) -> bool:
        """下载单个项目（使用指定Session）"""
        if session is None:
            session = self.session
            
        item_success = True
        
        # 下载网页
        if item.dzdc_url:
            if download_images:
                if not self.download_webpage_with_assets_with_session(item.dzdc_url, web_path, item.filename_base, attach_path, session):
                    item_success = False
            else:
                if not self.download_webpage_simple_with_session(item.dzdc_url, web_path, item.filename_base, session):
                    item_success = False

        # 下载原始文件
        if item.download_url:
            success, downloaded_filename = self.download_raw_file_with_session(item.download_url, raw_path, item.filename_base, session)
            if not success:
                item_success = False
            else:
                logger.info(f"  原始文件保存为: {downloaded_filename}")
                
        # 保存JSON
        if not self.save_json_file(item, json_path):
            item_success = False

        return item_success
    
    def _download_single_item_safe(self, item: DownloadItem, web_path: str, raw_path: str, json_path: str, attach_path: str, download_images: bool = True) -> bool:
        """安全的下载单个项目，带有错误处理和独立Session"""
        try:
            # 为每个线程创建独立的Session
            thread_session = self._create_thread_session()
            return self.download_single_item_with_session(item, web_path, raw_path, json_path, attach_path, download_images, thread_session)
        except Exception as e:
            logger.error(f"下载项目时发生异常: {item.filename_base} - {e}")
            return False
    
    def download_target_content(self, target_urls_info: List[DownloadItem], site_name: str, forecast_name: str, download_images: bool = True) -> Tuple[int, int]:
        """主下载函数，支持并发下载"""
        logger.info(f"开始下载任务...")
        logger.info(f"基础路径: {self.base_path}")
        logger.info(f"站点名称: {site_name}")
        logger.info(f"预测名称: {forecast_name}")
        logger.info("-" * 50)

        # 创建目录结构
        web_path, raw_path, json_path, attach_path = self.create_directories(site_name, forecast_name)
        logger.info(f"创建目录:")
        logger.info(f"  网页目录: {web_path}")
        logger.info(f"  原始文件目录: {raw_path}")
        logger.info(f"  JSON目录: {json_path}")
        logger.info(f"  附件目录: {attach_path}")

        logger.info("-" * 50)

        # 在并发下载前统一检查token
        self._ensure_session_token()

        # 开始并发下载
        total_items = len(target_urls_info)
        success_count = 0
        failed_items = []

        with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交所有下载任务
            future_to_item = {
                executor.submit(
                    self._download_single_item_safe, 
                    item, web_path, raw_path, json_path, attach_path, download_images
                ): item for item in target_urls_info
            }

            with tqdm(total=total_items, desc="总体进度", unit="项") as pbar:
                for future in concurrent.futures.as_completed(future_to_item):
                    item = future_to_item[future]
                    try:
                        result = future.result()
                        if result:
                            success_count += 1
                            logger.info(f"✓ {item.filename_base} 处理完成")
                        else:
                            failed_items.append(item.filename_base)
                            logger.error(f"✗ {item.filename_base} 处理失败")
                    except Exception as e:
                        failed_items.append(item.filename_base)
                        logger.error(f"✗ {item.filename_base} 处理异常: {e}")
                    
                    pbar.update(1)
                    
                    # 定期检查token有效性，每处理100个项目检查一次
                    if pbar.n % Config.TOKEN_CHECK_INTERVAL == 0:
                        self.batch_ensure_token()

        logger.info("-" * 50)
        logger.info(f"下载任务完成!")
        logger.info(f"成功处理: {success_count}/{total_items} 项")
        if failed_items:
            logger.info(f"失败项: {', '.join(failed_items[:10])}")
            if len(failed_items) > 10:
                logger.info(f"... 等共{len(failed_items)}项失败")
        logger.info(f"文件保存在: {os.path.join(self.base_path, site_name, forecast_name)}")

        return success_count, total_items
    
    def process_single_group(self, group: ParameterGroup, download_images: bool = True) -> Tuple[int, int, int]:
        """处理单个参数组"""
        logger.info(f"开始处理: {group}")
        
        # 优化：一次性获取总数和所有数据
        total, all_rows = self.get_total_and_data(group.site_id, group.forecast_method)
        if total == 0:
            logger.warning(f"未找到数据: {group}")
            return 0, 0, 0
        
        logger.info(f"总记录数: {total}")
        
        if not all_rows:
            logger.warning(f"获取数据失败: {group}")
            return 0, 0, total
        
        # 构建URL
        target_urls_info = self.batch_build_urls(all_rows, group.forecast_method)
        logger.info(f"构造URL数量: {len(target_urls_info)}")
        
        # 过滤已存在的数据
        target_urls_info_not_existing, stats_message = self.filter_existing_data(
            target_urls_info, group.site_name, group.forecast_name
        )
        logger.info(f"过滤结果: {stats_message}")
        
        if not target_urls_info_not_existing:
            logger.info(f"所有数据已存在，跳过下载: {group}")
            return len(target_urls_info), len(target_urls_info), total
        
        # 下载数据
        success_count, download_count = self.download_target_content(
            target_urls_info_not_existing, group.site_name, group.forecast_name, download_images=download_images
        )
        
        return success_count, download_count, total
    
    def run(self, parameter_groups: List[ParameterGroup], download_images: bool = True) -> Dict:
        """运行爬虫"""
        logger.info(f"开始执行爬虫任务，共{len(parameter_groups)}个参数组")
        logger.info(f"并发工作线程数: {self.max_workers}")
        
        # 预先确保token有效
        if not self.token_manager.ensure_valid_token():
            logger.error("无法获取有效Token，终止任务")
            return {}
        
        total_success = 0
        total_download = 0
        total_available = 0
        
        results = {}
        
        for group in parameter_groups:
            group_key = f"{group.site_name}_{group.forecast_name}"
            success, download, available = self.process_single_group(group, download_images)
            
            results[group_key] = {
                'success': success,
                'download': download,
                'available': available,
                'success_rate': f"{success/download*100:.1f}%" if download > 0 else "0%"
            }
            
            total_success += success
            total_download += download
            total_available += available
            
            # 组间延迟
            time.sleep(1)
        
        logger.info("=" * 60)
        logger.info("爬虫任务完成!")
        logger.info(f"总计: 成功{total_success}/{total_download}项下载，共{total_available}项可用")
        logger.info(f"总体成功率: {total_success/total_download*100:.1f}%" if total_download > 0 else "总体成功率: 0%")
        
        return results
    
    def clear_token_cache(self):
        """清除token缓存"""
        self.token_manager.cache.clear(self.token_manager.appname, self.token_manager.account)
        logger.info("Token缓存已清除")
    
    def get_token_status(self) -> Dict:
        """获取token状态信息"""
        # 确保缓存已加载
        if not self.token_manager._cache_loaded:
            self.token_manager._load_from_cache()
        
        # 获取当前的过期时间
        current_expired_time = self.token_manager.expired_time
        
        return {
            'has_token': bool(self.token_manager.token),
            'expired_time': current_expired_time,
            'is_valid': self.token_manager.ensure_valid_token(),
            'cache_exists': Path(f".token_cache/{hashlib.md5(f'{self.token_manager.appname}:{self.token_manager.account}'.encode()).hexdigest()}.pkl").exists()
        }

def load_config(config_path: str = "config.json") -> Dict:
    """加载配置文件"""
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        logger.info(f"配置文件加载成功: {config_path}")
        return config
    except FileNotFoundError:
        logger.error(f"配置文件不存在: {config_path}")
        return {}
    except json.JSONDecodeError as e:
        logger.error(f"配置文件格式错误: {e}")
        return {}
    except Exception as e:
        logger.error(f"加载配置文件失败: {e}")
        return {}

def main():
    """主函数"""
    import argparse
    
    # 添加命令行参数支持
    parser = argparse.ArgumentParser(description="地质超前预报数据爬虫")
    parser.add_argument("--config", default="config.json", help="配置文件路径")
    parser.add_argument("--clear-cache", action="store_true", help="清除token缓存")
    parser.add_argument("--token-status", action="store_true", help="显示token状态")
    parser.add_argument("--base-path", help="基础路径，覆盖配置文件")
    parser.add_argument("--max-workers", type=int, help="最大并发数，覆盖配置文件")
    parser.add_argument("--no-images", action="store_true", help="不下载图片资源")
    
    args = parser.parse_args()
    
    # 处理特殊命令
    if args.clear_cache:
        # 为清除缓存命令也加载配置
        config = load_config(args.config)
        crawler = DZYBCrawler("", config=config)
        crawler.clear_token_cache()
        return
    
    if args.token_status:
        # 为token状态命令也加载配置
        config = load_config(args.config)
        crawler = DZYBCrawler("", config=config)
        status = crawler.get_token_status()
        print("Token状态:")
        for key, value in status.items():
            print(f"  {key}: {value}")
        return
    
    # 加载配置文件
    config = load_config(args.config)
    
    if not config:
        logger.error("无法加载配置文件，使用默认配置")
        # 使用默认配置
        base_path = args.base_path or r"D:\BaiduSyncdisk\DFN\SynchronizeData"
        max_workers = args.max_workers or 3
        download_images = not args.no_images
        parameter_groups = [
            ParameterGroup(230267, "CZXZZQ_10果拉山隧道进口右线", 7, "掌子面素描"),
        ]
    else:
        # 从配置文件读取参数
        base_path = args.base_path or config.get("base_path", r"D:\BaiduSyncdisk\DFN\SynchronizeData")
        max_workers = args.max_workers or config.get("max_workers", 3)
        download_images = not args.no_images and config.get("download_images", True)
        
        # 设置日志级别
        log_level = config.get("log_level", "INFO")
        logging.getLogger().setLevel(getattr(logging, log_level))
        
        # 构建参数组
        parameter_groups = []
        parameter_groups_config = config.get("parameter_groups", {})
        
        # 遍历所有站点
        for site_name, site_config in parameter_groups_config.items():
            # 检查站点是否启用
            if site_config.get("enabled", False):
                site_id = site_config["site_id"]
                # 遍历站点的所有方法
                for method_name, method_config in site_config.get("methods", {}).items():
                    # 检查方法是否启用
                    if method_config.get("enabled", False):
                        parameter_groups.append(ParameterGroup(
                            site_id=site_id,
                            site_name=site_name,
                            forecast_method=method_config["method_id"],
                            forecast_name=method_name
                        ))
        
        # 记录启用的站点和方法
        enabled_sites = []
        for site_name, site_config in parameter_groups_config.items():
            if site_config.get("enabled", False):
                enabled_methods = [method_name for method_name, method_config in site_config.get("methods", {}).items() if method_config.get("enabled", False)]
                enabled_sites.append(f"{site_name}({len(enabled_methods)}方法)")
        
        logger.info(f"启用的站点: {', '.join(enabled_sites) if enabled_sites else '无'}")
        
        # 如果配置文件中没有启用的参数组，使用默认的
        if not parameter_groups:
            logger.warning("没有启用的参数组，使用默认配置")
            parameter_groups = [
                ParameterGroup(230267, "CZXZZQ_10果拉山隧道进口右线", 7, "掌子面素描"),
            ]
    
    logger.info(f"使用配置: base_path={base_path}, max_workers={max_workers}, download_images={download_images}")
    logger.info(f"参数组数量: {len(parameter_groups)}")
    
    # 创建爬虫实例
    crawler = DZYBCrawler(base_path, max_workers, config)
    
    # 显示token状态
    token_status = crawler.get_token_status()
    expired_time_str = token_status['expired_time'] if token_status['expired_time'] else "未知"
    logger.info(f"Token状态: 有效={token_status['is_valid']}, 过期时间={expired_time_str}")
    
    # 运行爬虫，传递配置参数
    results = crawler.run(parameter_groups, download_images=download_images)
    
    # 打印结果摘要
    print("\n" + "=" * 60)
    print("任务结果摘要:")
    for group_key, result in results.items():
        print(f"{group_key}: {result['success']}/{result['download']} 成功, {result['available']} 可用")
    print("=" * 60)

if __name__ == "__main__":
    main()