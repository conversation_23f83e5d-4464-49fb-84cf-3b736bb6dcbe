import rsa
import json
import requests
from time import time

# # 将Base64编码的公钥字符串转换为PEM格式的公钥
# def convert_public_key_to_pem(public_key_base64):
#     pem_key = "-----BEGIN PUBLIC KEY-----\n"
#     pem_key += '\n'.join(public_key_base64[i:i+64] for i in range(0, len(public_key_base64), 64))
#     pem_key += "\n-----END PUBLIC KEY-----"
#     return pem_key

# # Python版本的RSA加密函数，模拟Java中的RSAUtil.publicEncrypt方法
# def public_encrypt(public_key_base64, data):
#     # 将Base64格式的公钥转换为PEM格式
#     public_key_pem = convert_public_key_to_pem(public_key_base64)
#     public_key = rsa.PublicKey.load_pkcs1_openssl_pem(public_key_pem.encode('utf-8'))
    
#     # 将数据转换为字节
#     data_bytes = data.encode('utf-8')
    
#     # 使用公钥加密数据
#     encrypted_data = rsa.encrypt(data_bytes, public_key)
    
#     # 将加密后的数据转换为十六进制字符串
#     encrypted_hex = encrypted_data.hex()
    
#     return encrypted_hex

# # 主函数，模拟Java代码中的encrypt方法
# def encrypt(appname, account, password, timestamp):
#     # 公钥字符串（从Java代码中复制）
#     public_key_base64 = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCEJiVVdmEzgRk/Lqg+I8JyavmI6eRZCv9d3rJ/yDGUS0BbxrrUrhdK4t/hLZUjkjtCsX80eLxecn6HkMBHaEM0tQYmZyxpB5NBPHioggQHDrJVskzDdqliHobem+X54INPznlXXwLtRl4vh1/ducI7laD59bVg/I+h/SrepgOvqwIDAQAB"
    
#     # 创建JSON对象
#     data = {
#         "timestamp": timestamp,
#         "appname": appname,
#         "password": password,
#         "account": account
#     }
    
#     # 将JSON对象转换为字符串
#     data_str = json.dumps(data, ensure_ascii=False)
    
#     # 使用RSA公钥加密数据
#     encrypted_data = public_encrypt(public_key_base64, data_str)
    
#     return encrypted_data

# # 发送POST请求的函数
# def send_post_request(appname, account, password, timestamp, encrypted_token, real_url):
#     # 请求数据
#     data = {
#         'appname': appname,
#         'account': account,
#         'password': password,
#         'timestamp': timestamp,
#         'token': encrypted_token,
#         'realUrl': real_url
#     }

#     # 构造请求URL
#     api_url = 'https://sso.r93535.com/api/v1.0/login'  # 替换为实际的API地址

#     # 将字典数据转换为URL编码格式
#     encoded_data = urlencode(data)

#     # 设置请求头部
#     headers = {
#         'Content-Type': 'application/x-www-form-urlencoded'
#     }

#     # 发送POST请求
#     response = requests.post(api_url, data=encoded_data, headers=headers)

#     # 打印响应内容
#     print(response.text)
#     return response

# # 测试数据
# appname = "yjjy"
# account = "czwlj"
# password = "uFtxY2UoudZ57IdfVokl8Q=="
# timestamp = str(int(round(time() * 1000)))  # 当前时间戳
# real_url = "https://sso.r93535.com/api/v1.0/login"

# # 调用加密函数
# encrypted_token = encrypt(appname, account, password, timestamp)

# # 调用发送POST请求的函数
# response = send_post_request(appname, account, password, timestamp, encrypted_token, real_url)
# response_json = response.json()  

# # 假设您已经有了JWT token
# jwt_token = response_json['jwt']

# print(response.status_code == 200)
# print(response.json().get('jwt'))


def encrypt_data(appname, account, password, timestamp):
    # 公钥
    public_key_base64 = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCEJiVVdmEzgRk/Lqg+I8JyavmI6eRZCv9d3rJ/yDGUS0BbxrrUrhdK4t/hLZUjkjtCsX80eLxecn6HkMBHaEM0tQYmZyxpB5NBPHioggQHDrJVskzDdqliHobem+X54INPznlXXwLtRl4vh1/ducI7laD59bVg/I+h/SrepgOvqwIDAQAB"
    
    # 转换为PEM格式并加载公钥
    pem_key = f"-----BEGIN PUBLIC KEY-----\n{public_key_base64}\n-----END PUBLIC KEY-----"
    public_key = rsa.PublicKey.load_pkcs1_openssl_pem(pem_key.encode())
    
    # 创建数据并加密
    data = json.dumps({
        "timestamp": timestamp,
        "appname": appname,
        "password": password,
        "account": account
    })
    
    return rsa.encrypt(data.encode(), public_key).hex()

def login(appname, account, password):
    timestamp = str(int(time() * 1000))
    token = encrypt_data(appname, account, password, timestamp)
    
    response = requests.post(
        'https://sso.r93535.com/api/v1.0/login',
        data={
            'appname': appname,
            'account': account,
            'password': password,
            'timestamp': timestamp,
            'token': token,
            'realUrl': 'https://sso.r93535.com/api/v1.0/login'
        },
        headers = {
            'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'application/json, text/javascript, */*; q=0.01',
            'X-Requested-With': 'XMLHttpRequest',
            'X-Forwarded-For': "*************",  
        }
    )
    
    return response.json()

# 使用示例
result = login("yjjy", "czwlj", "uFtxY2UoudZ57IdfVokl8Q==")
# result = login("yjjy", "xnjdwz", "4H0pJdYvfJxUPTs2BdiTnw==")
# result = login("yjjy", "czcxzx02", "/OYSKZ6fxjZibot6QLpMXA==")

print(result)
jwt_token = result['jwt']
print(jwt_token)
print(result['expiredtime'])
# # proxy_addon.py
# import time
# import logging
# from typing import Optional
# from mitmproxy import http

# class AddCookieHeader:
#     def __init__(self):
#         self.jwt_token: Optional[str] = None
#         self.token_expire_time: float = 0
#         self.max_retries = 3

#     def _get_jwt_token(self) -> Optional[str]:
#         try:
#             appname = "yjjy"
#             account = "czwlj"
#             password = "uFtxY2UoudZ57IdfVokl8Q=="
#             timestamp = str(int(round(time() * 1000)))  # 当前时间戳
#             real_url = "https://sso.r93535.com/api/v1.0/login"
            
#             encrypted_token = encrypt(appname, account, password, timestamp)
#             response = send_post_request(
#                 appname, account, password, timestamp,
#                 encrypted_token, real_url
#             )
            
#             if response.status_code == 200:
#                 return response.json().get('jwt')
#             logging.error(f"Token请求失败: {response.text}")
#         except Exception as e:
#             logging.error(f"获取Token异常: {str(e)}")
#         return None

#     def _need_refresh_token(self) -> bool:
#         return (not self.jwt_token) or (time.time() > self.token_expire_time)

#     def request(self, flow: http.HTTPFlow) -> None:
#         # 关键过滤逻辑：非目标域名直接放行
#         if "apps.r93535.com" not in flow.request.pretty_host:
#             flow.reply()  # 不进行中间人拦截
#             return

#         # Token刷新逻辑
#         if self._need_refresh_token():
#             new_token = self._get_jwt_token()
#             if new_token:
#                 self.jwt_token = new_token
#                 self.token_expire_time = time.time() + 3600
#                 self.max_retries = 3
#             else:
#                 logging.error("Token获取失败，保留最后一次有效Token")

#         # 注入Cookie头
#         if self.jwt_token:
#             flow.request.headers["Cookie"] = f"CRBIMSSOJWT={self.jwt_token}"
#         else:
#             flow.response = http.Response.make(401, b"Unauthorized")

# addons = [AddCookieHeader()]