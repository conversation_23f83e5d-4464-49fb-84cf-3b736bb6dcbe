import requests
from Crypto.PublicKey import RSA
from Crypto.Cipher import PKCS1_v1_5
import base64
import json
from datetime import datetime

# RSA Public Key (Base64 Encoded)
public_key_base64 = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCEJiVVdmEzgRk/Lqg+I8JyavmI6eRZCv9d3rJ/yDGUS0BbxrrUrhdK4t/hLZUjkjtCsX80eLxecn6HkMBHaEM0tQYmZyxpB5NBPHioggQHDrJVskzDdqliHobem+X54INPznlXXwLtRl4vh1/ducI7laD59bVg/I+h/SrepgOvqwIDAQAB"

# Decode the Base64 encoded public key
public_key = RSA.import_key(base64.b64decode(public_key_base64))

# Function to encrypt data using RSA
def rsa_encrypt(data):
    cipher = PKCS1_v1_5.new(public_key)
    encrypted_data = cipher.encrypt(data.encode())
    return base64.b64encode(encrypted_data).decode()

# Function to simulate the login process
def login(appname, account, password):
    timestamp = str(int(datetime.now().timestamp() * 1000))
    data = {
        "appname": appname,
        "account": account,
        "password": password,
        "timestamp": timestamp
    }
    data_json = json.dumps(data)
    encrypted_token = rsa_encrypt(data_json)
    return encrypted_token, timestamp

# Function to send the login request
def send_login_request(encrypted_token, timestamp):
    data_with_token = {
        "appname": "yjjy",
        "account": "czwlj",
        "password": "uFtxY2UoudZ57IdfVokl8Q==",
        "timestamp": timestamp,
        "token": encrypted_token
    }
    response = requests.post('https://sso.r93535.com/api/v1.0/login', data=data_with_token)
    return response.json()

# Main process
if __name__ == "__main__":
    appname = "yjjy"
    account = "czwlj"
    password = "uFtxY2UoudZ57IdfVokl8Q=="  # This should be the encrypted password

    # Simulate the login process
    encrypted_token, timestamp = login(appname, account, password)

    # Send the login request and get the response
    login_response = send_login_request(encrypted_token, timestamp)

    # Output the token
    print("Token:", login_response.get("token"))
