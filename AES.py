import base64
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad
from datetime import datetime


class AESUtils:
    # 使用与Java代码完全相同的密钥和初始化向量
    KEY = "r93535.com/.!!!!"
    INITVECTOR = "0987654321234567"
    
    @staticmethod
    def encrypt(value):
        """
        加密字符串
        :param value: 要加密的明文字符串
        :return: Base64编码的加密字符串
        """
        # 将密钥和IV转换为字节
        key = AESUtils.KEY.encode('utf-8')
        iv = AESUtils.INITVECTOR.encode('utf-8')
        
        # 创建AES cipher对象，使用CBC模式
        cipher = AES.new(key, AES.MODE_CBC, iv)
        
        # 将明文转换为字节并进行PKCS5/PKCS7填充
        # 注意：在Python中，PKCS5和PKCS7填充是相同的
        plaintext_bytes = value.encode('utf-8')
        padded_bytes = pad(plaintext_bytes, AES.block_size)
        
        # 加密
        encrypted_bytes = cipher.encrypt(padded_bytes)
        
        # Base64编码并返回字符串
        return base64.b64encode(encrypted_bytes).decode('utf-8')
    
    @staticmethod
    def decrypt(encrypted):
        """
        解密字符串
        :param encrypted: Base64编码的加密字符串
        :return: 解密后的明文字符串
        """
        # 将密钥和IV转换为字节
        key = AESUtils.KEY.encode('utf-8')
        iv = AESUtils.INITVECTOR.encode('utf-8')
        
        # 创建AES cipher对象，使用CBC模式
        cipher = AES.new(key, AES.MODE_CBC, iv)
        
        # Base64解码
        encrypted_bytes = base64.b64decode(encrypted)
        
        # 解密
        decrypted_bytes = cipher.decrypt(encrypted_bytes)
        
        # 去除填充并转换为字符串
        unpadded_bytes = unpad(decrypted_bytes, AES.block_size)
        return unpadded_bytes.decode('utf-8')


def main():
    """测试函数"""
    # 创建日期格式化对象
    sdf = datetime.now().strftime("%Y%m%d")
    
    try:
        # 测试加密
        test_string = "Lk.9619515"
        # test_string = "Lk.4645066"
        encrypted = AESUtils.encrypt(test_string)
        print(f"原文: {test_string}")
        print(f"加密后: {encrypted}")
        
        # 测试解密
        decrypted = AESUtils.decrypt("uFtxY2UoudZ57IdfVokl8Q==")
        print(f"解密后: {decrypted}")
        
        # 验证加密解密是否正确
        if test_string == decrypted:
            print("加密解密验证成功！")
        
        # 这会抛出异常，因为"sdfa"不是有效的Base64编码的加密数据
        # print(AESUtils.decrypt("sdfa"))
        
    except Exception as e:
        print(f"错误: {e}")


# 如果需要与Java代码交互，可以使用以下函数进行测试
def test_java_compatibility():
    """测试与Java代码的兼容性"""
    # 如果你有Java代码生成的加密字符串，可以在这里测试解密
    # java_encrypted = "你的Java加密字符串"
    # print(AESUtils.decrypt(java_encrypted))
    pass


if __name__ == "__main__":
    main()
    # test_java_compatibility()