from flask import Flask, redirect, make_response, request

app = Flask(__name__)

# 替换为您的JWT token
jwt_token = '您的JWT_token'

@app.route('/set-jwt-cookie')
def set_jwt_cookie():
    # 设置cookie
    resp = make_response(redirect('https://apps.r93535.com/cqdzyb/index/index'))
    resp.set_cookie('CRBIMSSOJWT', jwt_token, domain='.r93535.com', path='/')
    return resp

if __name__ == '__main__':
    app.run(port=3000)




# from flask import Flask, make_response, redirect

# app = Flask(__name__)

# TARGET_DOMAIN = 'https://targetdomain.com'  # 替换为目标域名
# JWT_TOKEN = 'your.jwt.token'  # 替换为你的JWT

# @app.route('/set-jwt-cookie')
# def set_jwt_cookie():
#     # 创建响应对象
#     resp = make_response(redirect(TARGET_DOMAIN))
#     # 设置cookie，类似于JavaScript中的document.cookie设置方式
#     resp.set_cookie('CRBIMSSOJWT', JWT_TOKEN, domain='.targetdomain.com', path='/', httponly=True, secure=True)
#     return resp

# if __name__ == '__main__':
#     app.run(port=3000)
